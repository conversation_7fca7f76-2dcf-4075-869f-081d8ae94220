<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My TradingView Chart Viewer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }
        
        body {
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        h1 {
            font-size: 28px;
            margin-bottom: 10px;
            color: #2c6ecf;
        }
        
        .settings-panel {
            background-color: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        
        .button-group {
            margin-top: 20px;
            display: flex;
            gap: 10px;
        }
        
        button {
            padding: 10px 20px;
            background-color: #2c6ecf;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #205bb0;
        }
        
        button.secondary {
            background-color: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
        }
        
        button.secondary:hover {
            background-color: #e0e0e0;
        }
        
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 10px 20px;
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-bottom: none;
            border-radius: 4px 4px 0 0;
            cursor: pointer;
            font-weight: 500;
        }
        
        .tab.active {
            background-color: white;
            border-bottom: 1px solid white;
            position: relative;
            top: 1px;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .chart-container {
            height: 600px;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            background-color: white;
        }
        
        .help-text {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        
        .error-message {
            color: #d32f2f;
            font-size: 14px;
            margin-top: 5px;
            display: none;
        }
        
        footer {
            text-align: center;
            margin-top: 40px;
            font-size: 14px;
            color: #666;
        }
        
        .hidden {
            display: none;
        }
        
        .collapsible {
            background-color: #f9f9f9;
            border-radius: 4px;
            margin-top: 20px;
            overflow: hidden;
        }
        
        .collapsible-header {
            padding: 15px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #f1f1f1;
        }
        
        .collapsible-header h3 {
            margin: 0;
            font-size: 16px;
        }
        
        .collapsible-content {
            padding: 0 15px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out;
        }
        
        .collapsible.open .collapsible-content {
            max-height: 1000px;
            padding: 15px;
        }
        
        .steps {
            margin-top: 15px;
        }
        
        .step {
            margin-bottom: 15px;
            display: flex;
            gap: 15px;
        }
        
        .step-number {
            background-color: #2c6ecf;
            color: white;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            flex-shrink: 0;
        }
        
        .step-content h4 {
            margin: 0 0 5px 0;
            font-size: 16px;
        }
        
        .step-content p {
            margin: 0;
            font-size: 14px;
        }
        
        code {
            background-color: #f5f5f5;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 14px;
        }
        
        /* Dark mode */
        .dark-mode {
            background-color: #1a1a1a;
            color: #f5f5f5;
        }
        
        .dark-mode .settings-panel {
            background-color: #2d2d2d;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        .dark-mode h1 {
            color: #4dabf5;
        }
        
        .dark-mode input, .dark-mode select {
            background-color: #3d3d3d;
            border-color: #555;
            color: #f5f5f5;
        }
        
        .dark-mode .tab {
            background-color: #2d2d2d;
            border-color: #555;
            color: #f5f5f5;
        }
        
        .dark-mode .tab.active {
            background-color: #3d3d3d;
            border-bottom-color: #3d3d3d;
        }
        
        .dark-mode .chart-container {
            background-color: #2d2d2d;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        
        .dark-mode .collapsible {
            background-color: #2d2d2d;
        }
        
        .dark-mode .collapsible-header {
            background-color: #3d3d3d;
        }
        
        .dark-mode code {
            background-color: #333;
            color: #f5f5f5;
        }
        
        .dark-mode button.secondary {
            background-color: #3d3d3d;
            color: #f5f5f5;
            border-color: #555;
        }
        
        .dark-mode button.secondary:hover {
            background-color: #4d4d4d;
        }
        
        .dark-mode .help-text {
            color: #aaa;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .button-group {
                flex-direction: column;
            }
            
            .chart-container {
                height: 400px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>My TradingView Chart Viewer</h1>
            <p>Display your saved TradingView charts directly in your browser</p>
        </header>
        
        <div class="settings-panel">
            <div class="tabs">
                <div class="tab active" data-tab="saved-chart">Saved Chart</div>
                <div class="tab" data-tab="symbol">Symbol</div>
            </div>
            
            <div class="tab-content active" id="saved-chart-tab">
                <div class="form-group">
                    <label for="username">TradingView Username:</label>
                    <input type="text" id="username" placeholder="Your TradingView username">
                    <p class="help-text">Your TradingView username, not your email (e.g., the name in your profile URL)</p>
                    <p class="error-message" id="username-error">Please enter a valid username</p>
                </div>
                
                <div class="form-group">
                    <label for="chart-id">Chart ID:</label>
                    <input type="text" id="chart-id" placeholder="Your chart ID from TradingView">
                    <p class="help-text">The ID from your saved chart URL (e.g., the string after /chart/ in the URL)</p>
                    <p class="error-message" id="chart-id-error">Please enter a valid chart ID</p>
                </div>
            </div>
            
            <div class="tab-content" id="symbol-tab">
                <div class="form-group">
                    <label for="symbol">Symbol:</label>
                    <input type="text" id="symbol" placeholder="Enter symbol (e.g., AAPL, EURUSD, ES)" value="ES">
                    <p class="help-text">Enter a valid TradingView symbol</p>
                </div>
                
                <div class="form-group">
                    <label for="interval">Timeframe:</label>
                    <select id="interval">
                        <option value="1">1 Minute</option>
                        <option value="5">5 Minutes</option>
                        <option value="15">15 Minutes</option>
                        <option value="30">30 Minutes</option>
                        <option value="60">1 Hour</option>
                        <option value="240">4 Hours</option>
                        <option value="D" selected>1 Day</option>
                        <option value="W">1 Week</option>
                        <option value="M">1 Month</option>
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label for="theme">Theme:</label>
                <select id="theme">
                    <option value="light">Light</option>
                    <option value="dark">Dark</option>
                </select>
            </div>
            
            <div class="button-group">
                <button id="load-chart">Load Chart</button>
                <button id="toggle-fullscreen" class="secondary">Toggle Fullscreen</button>
                <button id="toggle-mode" class="secondary">Toggle Dark Mode</button>
            </div>
        </div>
        
        <div id="chart-container" class="chart-container"></div>
        
        <div class="collapsible">
            <div class="collapsible-header">
                <h3>How to Find Your Chart ID and Username</h3>
                <span class="toggle-icon">+</span>
            </div>
            <div class="collapsible-content">
                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h4>Log in to TradingView</h4>
                            <p>Make sure you're logged into your TradingView account at <a href="https://www.tradingview.com/" target="_blank">tradingview.com</a>.</p>
                        </div>
                    </div>
                    
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h4>Open your saved chart</h4>
                            <p>Navigate to the chart you want to display in this viewer.</p>
                        </div>
                    </div>
                    
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h4>Find the chart ID in the URL</h4>
                            <p>Look at your browser's address bar. You'll see a URL like:</p>
                            <p><code>https://www.tradingview.com/chart/XXXXXXXXXXXX/</code></p>
                            <p>The part after "/chart/" and before the next slash is your chart ID.</p>
                        </div>
                    </div>
                    
                    <div class="step">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <h4>Find your username</h4>
                            <p>Your TradingView username is not your email. It's the name in your profile URL:</p>
                            <p><code>https://www.tradingview.com/u/YOUR_USERNAME/</code></p>
                            <p>You can find this by clicking on your profile icon and then your name.</p>
                        </div>
                    </div>
                </div>
                
                <div class="notes">
                    <h4>Notes:</h4>
                    <ul>
                        <li>Your chart must be saved to your TradingView account to be accessible.</li>
                        <li>This viewer doesn't store any of your data; it simply displays your chart.</li>
                        <li>Some advanced features may only be available with a TradingView subscription.</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <footer>
        <p>TradingView Chart Viewer &copy; 2025 | All chart data provided by TradingView</p>
    </footer>
    
    <script type="text/javascript">
        document.addEventListener('DOMContentLoaded', function() {
            // Elements
            const chartContainer = document.getElementById('chart-container');
            const loadChartButton = document.getElementById('load-chart');
            const toggleFullscreenButton = document.getElementById('toggle-fullscreen');
            const toggleModeButton = document.getElementById('toggle-mode');
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');
            const usernameInput = document.getElementById('username');
            const chartIdInput = document.getElementById('chart-id');
            const symbolInput = document.getElementById('symbol');
            const intervalSelect = document.getElementById('interval');
            const themeSelect = document.getElementById('theme');
            const collapsibleHeader = document.querySelector('.collapsible-header');
            const body = document.body;
            
            // Variables
            let tvWidget = null;
            let scriptElement = null;
            
            // Tab switching
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // Remove active class from all tabs and contents
                    tabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));
                    
                    // Add active class to clicked tab
                    tab.classList.add('active');
                    
                    // Show corresponding content
                    const tabId = tab.getAttribute('data-tab');
                    document.getElementById(`${tabId}-tab`).classList.add('active');
                });
            });
            
            // Collapsible section
            collapsibleHeader.addEventListener('click', () => {
                const collapsible = collapsibleHeader.parentElement;
                collapsible.classList.toggle('open');
                const toggleIcon = collapsibleHeader.querySelector('.toggle-icon');
                toggleIcon.textContent = collapsible.classList.contains('open') ? '-' : '+';
            });
            
            // Load chart
            loadChartButton.addEventListener('click', () => {
                loadChart();
            });
            
            // Toggle fullscreen
            toggleFullscreenButton.addEventListener('click', () => {
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                } else {
                    chartContainer.requestFullscreen();
                }
            });
            
            // Toggle dark mode
            toggleModeButton.addEventListener('click', () => {
                body.classList.toggle('dark-mode');
            });
            
            // Theme change
            themeSelect.addEventListener('change', () => {
                if (tvWidget) {
                    loadChart(); // Reload chart with new theme
                }
            });
            
            // Load chart function
            function loadChart() {
                // Clean up any existing chart
                cleanupChart();
                
                // Get values
                const theme = themeSelect.value;
                const activeTab = document.querySelector('.tab.active').getAttribute('data-tab');
                
                // Create script element
                scriptElement = document.createElement('script');
                scriptElement.src = 'https://s3.tradingview.com/tv.js';
                scriptElement.async = true;
                scriptElement.onload = () => {
                    // Create widget options
                    const widgetOptions = {
                        autosize: true,
                        container_id: 'chart-container',
                        theme: theme,
                        timezone: "America/New_York",
                        style: "1",
                        locale: "en",
                        toolbar_bg: theme === 'dark' ? '#2d2d2d' : '#f1f1f1',
                        enable_publishing: false,
                        hide_side_toolbar: false,
                        save_image: true,
                        studies: ['RSI@tv-basicstudies'],
                        show_popup_button: true,
                        popup_width: "1000",
                        popup_height: "650",
                        overrides: {
                            "mainSeriesProperties.style": 1,
                            "paneProperties.background": theme === 'dark' ? '#2d2d2d' : '#ffffff',
                            "paneProperties.vertGridProperties.color": theme === 'dark' ? '#444' : '#e0e0e0',
                            "paneProperties.horzGridProperties.color": theme === 'dark' ? '#444' : '#e0e0e0',
                            "symbolWatermarkProperties.transparency": 90,
                            "scalesProperties.textColor": theme === 'dark' ? '#aaa' : '#333',
                        }
                    };
                    
                    // Configure widget based on active tab
                    if (activeTab === 'saved-chart') {
                        const username = usernameInput.value.trim();
                        const chartId = chartIdInput.value.trim();
                        
                        // Validate inputs
                        if (!username) {
                            document.getElementById('username-error').style.display = 'block';
                            return;
                        } else {
                            document.getElementById('username-error').style.display = 'none';
                        }
                        
                        if (!chartId) {
                            document.getElementById('chart-id-error').style.display = 'block';
                            return;
                        } else {
                            document.getElementById('chart-id-error').style.display = 'none';
                        }
                        
                        // Add chart-specific options
                        widgetOptions.username = username;
                        widgetOptions.chart = chartId;
                    } else {
                        // Symbol mode
                        const symbol = symbolInput.value.trim();
                        const interval = intervalSelect.value;
                        
                        // Add symbol-specific options
                        widgetOptions.symbol = symbol;
                        widgetOptions.interval = interval;
                    }
                    
                    // Create widget
                    tvWidget = new TradingView.widget(widgetOptions);
                };
                
                // Add script to page
                document.body.appendChild(scriptElement);
            }
            
            // Cleanup function
            function cleanupChart() {
                // Remove existing widget
                if (tvWidget) {
                    tvWidget = null;
                }
                
                // Remove script element
                if (scriptElement) {
                    document.body.removeChild(scriptElement);
                    scriptElement = null;
                }
                
                // Clear container
                chartContainer.innerHTML = '';
                
                // Remove TradingView elements
                const tvElements = document.querySelectorAll('iframe[src*="tradingview.com"]');
                tvElements.forEach(el => el.remove());
            }
        });
    </script>
</body>
</html>