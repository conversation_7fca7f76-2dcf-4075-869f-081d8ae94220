# Using Your TradingView Saved Charts

This feature allows you to display your personal saved charts from TradingView directly in the MacroAnalysis application. This gives you the power to use your customized charts with all your preferred indicators, settings, and drawing tools.

## Benefits of Using Saved Charts

1. **Personalized Analysis**: Use your own custom chart setups with your preferred indicators and settings
2. **Consistent Experience**: See the same chart configuration you use on TradingView
3. **Advanced Features**: Access advanced TradingView features and indicators
4. **Time-Saving**: Avoid recreating your chart setup each time
5. **Familiarity**: Work with the tools and layouts you're already comfortable with

## Requirements

To use this feature, you need:

1. A TradingView account (free or paid)
2. At least one saved chart in your TradingView account
3. Your TradingView username
4. The chart ID of the specific chart you want to display

## How to Find Your Chart ID

1. **Log in to TradingView**: Make sure you're logged into your account at [tradingview.com](https://www.tradingview.com/)
2. **Open Your Saved Chart**: Navigate to the specific chart you want to display
3. **Check the URL**: In your browser's address bar, you'll see a URL like:
   ```
   https://www.tradingview.com/chart/XXXXXXXXXXXX/
   ```
4. **Identify Your Chart ID**: The part after "/chart/" and before the next slash is your chart ID (usually 12 characters)

## Your TradingView Username

Your TradingView username is not your email address. It's the user identifier that appears in your profile URL:

```
https://www.tradingview.com/u/YOUR_USERNAME/
```

You can find this by:
1. Going to your TradingView profile
2. Looking at the URL in your browser's address bar
3. Identifying the part after "/u/" which is your username

## Using the Feature

1. Navigate to the "Daily Fundamentals" tab
2. Click on the "TradingView" toggle button
3. Click the "Use Saved Chart" button
4. Enter your TradingView username
5. Enter your saved chart ID
6. Your saved chart will load with all your custom indicators and settings

## Compatibility Notes

- Some advanced features may only be available with a paid TradingView subscription
- Chart layouts will adapt to the size of the container in our application
- TradingView drawing tools and annotations will be preserved
- Chart preferences (theme, colors, etc.) will be preserved
- Price levels added in our application will be displayed on top of your TradingView chart

## Troubleshooting

If your chart doesn't load properly:

1. **Double-check your username**: Make sure you're using your TradingView username, not your email or display name
2. **Verify the chart ID**: Ensure you've copied the exact chart ID from the URL
3. **Check chart accessibility**: Make sure your chart is properly saved to your account
4. **Clear browser cache**: Try clearing your browser cache if you're having persistent issues
5. **Browser compatibility**: Ensure you're using a modern, up-to-date browser

## Privacy Considerations

- Using this feature does not share your TradingView account credentials
- Only the specific chart you select is displayed, not your entire account
- The chart is loaded directly from TradingView's servers using their public API
- Any changes you make to the chart in our application won't affect your saved chart on TradingView

## Additional Resources

- [TradingView Documentation](https://www.tradingview.com/support/)
- [TradingView Public Charts API](https://www.tradingview.com/charting-library-docs/)
- [TradingView User Guide](https://www.tradingview.com/chart-basics/)