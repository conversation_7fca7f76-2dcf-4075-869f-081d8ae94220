{"ast": null, "code": "import { useEffect } from 'react';\nimport useCommittedRef from './useCommittedRef';\n\n/**\n * Creates a `setInterval` that is properly cleaned up when a component unmounted\n *\n * ```tsx\n *  function Timer() {\n *    const [timer, setTimer] = useState(0)\n *    useInterval(() => setTimer(i => i + 1), 1000)\n *\n *    return <span>{timer} seconds past</span>\n *  }\n * ```\n *\n * @param fn an function run on each interval\n * @param ms The milliseconds duration of the interval\n */\n\n/**\n * Creates a pausable `setInterval` that is properly cleaned up when a component unmounted\n *\n * ```tsx\n *  const [paused, setPaused] = useState(false)\n *  const [timer, setTimer] = useState(0)\n *\n *  useInterval(() => setTimer(i => i + 1), 1000, paused)\n *\n *  return (\n *    <span>\n *      {timer} seconds past\n *\n *      <button onClick={() => setPaused(p => !p)}>{paused ? 'Play' : 'Pause' }</button>\n *    </span>\n * )\n * ```\n *\n * @param fn an function run on each interval\n * @param ms The milliseconds duration of the interval\n * @param paused Whether or not the interval is currently running\n */\n\n/**\n * Creates a pausable `setInterval` that _fires_ immediately and is\n * properly cleaned up when a component unmounted\n *\n * ```tsx\n *  const [timer, setTimer] = useState(-1)\n *  useInterval(() => setTimer(i => i + 1), 1000, false, true)\n *\n *  // will update to 0 on the first effect\n *  return <span>{timer} seconds past</span>\n * ```\n *\n * @param fn an function run on each interval\n * @param ms The milliseconds duration of the interval\n * @param paused Whether or not the interval is currently running\n * @param runImmediately Whether to run the function immediately on mount or unpause\n * rather than waiting for the first interval to elapse\n *\n\n */\n\nfunction useInterval(fn, ms, paused = false, runImmediately = false) {\n  let handle;\n  const fnRef = useCommittedRef(fn);\n  // this ref is necessary b/c useEffect will sometimes miss a paused toggle\n  // orphaning a setTimeout chain in the aether, so relying on it's refresh logic is not reliable.\n  const pausedRef = useCommittedRef(paused);\n  const tick = () => {\n    if (pausedRef.current) return;\n    fnRef.current();\n    schedule(); // eslint-disable-line no-use-before-define\n  };\n  const schedule = () => {\n    clearTimeout(handle);\n    handle = setTimeout(tick, ms);\n  };\n  useEffect(() => {\n    if (runImmediately) {\n      tick();\n    } else {\n      schedule();\n    }\n    return () => clearTimeout(handle);\n  }, [paused, runImmediately]);\n}\nexport default useInterval;", "map": {"version": 3, "names": ["useEffect", "useCommittedRef", "useInterval", "fn", "ms", "paused", "runImmediately", "handle", "fnRef", "pausedRef", "tick", "current", "schedule", "clearTimeout", "setTimeout"], "sources": ["/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/node_modules/@restart/ui/node_modules/@restart/hooks/esm/useInterval.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport useCommittedRef from './useCommittedRef';\n\n/**\n * Creates a `setInterval` that is properly cleaned up when a component unmounted\n *\n * ```tsx\n *  function Timer() {\n *    const [timer, setTimer] = useState(0)\n *    useInterval(() => setTimer(i => i + 1), 1000)\n *\n *    return <span>{timer} seconds past</span>\n *  }\n * ```\n *\n * @param fn an function run on each interval\n * @param ms The milliseconds duration of the interval\n */\n\n/**\n * Creates a pausable `setInterval` that is properly cleaned up when a component unmounted\n *\n * ```tsx\n *  const [paused, setPaused] = useState(false)\n *  const [timer, setTimer] = useState(0)\n *\n *  useInterval(() => setTimer(i => i + 1), 1000, paused)\n *\n *  return (\n *    <span>\n *      {timer} seconds past\n *\n *      <button onClick={() => setPaused(p => !p)}>{paused ? 'Play' : 'Pause' }</button>\n *    </span>\n * )\n * ```\n *\n * @param fn an function run on each interval\n * @param ms The milliseconds duration of the interval\n * @param paused Whether or not the interval is currently running\n */\n\n/**\n * Creates a pausable `setInterval` that _fires_ immediately and is\n * properly cleaned up when a component unmounted\n *\n * ```tsx\n *  const [timer, setTimer] = useState(-1)\n *  useInterval(() => setTimer(i => i + 1), 1000, false, true)\n *\n *  // will update to 0 on the first effect\n *  return <span>{timer} seconds past</span>\n * ```\n *\n * @param fn an function run on each interval\n * @param ms The milliseconds duration of the interval\n * @param paused Whether or not the interval is currently running\n * @param runImmediately Whether to run the function immediately on mount or unpause\n * rather than waiting for the first interval to elapse\n *\n\n */\n\nfunction useInterval(fn, ms, paused = false, runImmediately = false) {\n  let handle;\n  const fnRef = useCommittedRef(fn);\n  // this ref is necessary b/c useEffect will sometimes miss a paused toggle\n  // orphaning a setTimeout chain in the aether, so relying on it's refresh logic is not reliable.\n  const pausedRef = useCommittedRef(paused);\n  const tick = () => {\n    if (pausedRef.current) return;\n    fnRef.current();\n    schedule(); // eslint-disable-line no-use-before-define\n  };\n\n  const schedule = () => {\n    clearTimeout(handle);\n    handle = setTimeout(tick, ms);\n  };\n  useEffect(() => {\n    if (runImmediately) {\n      tick();\n    } else {\n      schedule();\n    }\n    return () => clearTimeout(handle);\n  }, [paused, runImmediately]);\n}\nexport default useInterval;"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,eAAe,MAAM,mBAAmB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,WAAWA,CAACC,EAAE,EAAEC,EAAE,EAAEC,MAAM,GAAG,KAAK,EAAEC,cAAc,GAAG,KAAK,EAAE;EACnE,IAAIC,MAAM;EACV,MAAMC,KAAK,GAAGP,eAAe,CAACE,EAAE,CAAC;EACjC;EACA;EACA,MAAMM,SAAS,GAAGR,eAAe,CAACI,MAAM,CAAC;EACzC,MAAMK,IAAI,GAAGA,CAAA,KAAM;IACjB,IAAID,SAAS,CAACE,OAAO,EAAE;IACvBH,KAAK,CAACG,OAAO,CAAC,CAAC;IACfC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACd,CAAC;EAED,MAAMA,QAAQ,GAAGA,CAAA,KAAM;IACrBC,YAAY,CAACN,MAAM,CAAC;IACpBA,MAAM,GAAGO,UAAU,CAACJ,IAAI,EAAEN,EAAE,CAAC;EAC/B,CAAC;EACDJ,SAAS,CAAC,MAAM;IACd,IAAIM,cAAc,EAAE;MAClBI,IAAI,CAAC,CAAC;IACR,CAAC,MAAM;MACLE,QAAQ,CAAC,CAAC;IACZ;IACA,OAAO,MAAMC,YAAY,CAACN,MAAM,CAAC;EACnC,CAAC,EAAE,CAACF,MAAM,EAAEC,cAAc,CAAC,CAAC;AAC9B;AACA,eAAeJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}