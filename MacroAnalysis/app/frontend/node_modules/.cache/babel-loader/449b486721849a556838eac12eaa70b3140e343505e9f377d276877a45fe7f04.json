{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport Dropdown from './Dropdown';\nimport NavLink from './NavLink';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst NavDropdown = /*#__PURE__*/React.forwardRef(({\n  id,\n  title,\n  children,\n  bsPrefix,\n  className,\n  rootCloseEvent,\n  menuRole,\n  disabled,\n  active,\n  renderMenuOnMount,\n  menuVariant,\n  ...props\n}, ref) => {\n  /* NavItem has no additional logic, it's purely presentational. Can set nav item class here to support \"as\" */\n  const navItemPrefix = useBootstrapPrefix(undefined, 'nav-item');\n  return /*#__PURE__*/_jsxs(Dropdown, {\n    ref: ref,\n    ...props,\n    className: classNames(className, navItemPrefix),\n    children: [/*#__PURE__*/_jsx(Dropdown.Toggle, {\n      id: id,\n      eventKey: null,\n      active: active,\n      disabled: disabled,\n      childBsPrefix: bsPrefix,\n      as: NavLink,\n      children: title\n    }), /*#__PURE__*/_jsx(Dropdown.Menu, {\n      role: menuRole,\n      renderOnMount: renderMenuOnMount,\n      rootCloseEvent: rootCloseEvent,\n      variant: menuVariant,\n      children: children\n    })]\n  });\n});\nNavDropdown.displayName = 'NavDropdown';\nexport default Object.assign(NavDropdown, {\n  Item: Dropdown.Item,\n  ItemText: Dropdown.ItemText,\n  Divider: Dropdown.Divider,\n  Header: Dropdown.Header\n});", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "Dropdown", "NavLink", "jsx", "_jsx", "jsxs", "_jsxs", "NavDropdown", "forwardRef", "id", "title", "children", "bsPrefix", "className", "rootCloseEvent", "menuRole", "disabled", "active", "renderMenuOnMount", "menuVariant", "props", "ref", "navItemPrefix", "undefined", "Toggle", "eventKey", "childBsPrefix", "as", "<PERSON><PERSON>", "role", "renderOnMount", "variant", "displayName", "Object", "assign", "<PERSON><PERSON>", "ItemText", "Divider", "Header"], "sources": ["/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/node_modules/react-bootstrap/esm/NavDropdown.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport Dropdown from './Dropdown';\nimport NavLink from './NavLink';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst NavDropdown = /*#__PURE__*/React.forwardRef(({\n  id,\n  title,\n  children,\n  bsPrefix,\n  className,\n  rootCloseEvent,\n  menuRole,\n  disabled,\n  active,\n  renderMenuOnMount,\n  menuVariant,\n  ...props\n}, ref) => {\n  /* NavItem has no additional logic, it's purely presentational. Can set nav item class here to support \"as\" */\n  const navItemPrefix = useBootstrapPrefix(undefined, 'nav-item');\n  return /*#__PURE__*/_jsxs(Dropdown, {\n    ref: ref,\n    ...props,\n    className: classNames(className, navItemPrefix),\n    children: [/*#__PURE__*/_jsx(Dropdown.Toggle, {\n      id: id,\n      eventKey: null,\n      active: active,\n      disabled: disabled,\n      childBsPrefix: bsPrefix,\n      as: NavLink,\n      children: title\n    }), /*#__PURE__*/_jsx(Dropdown.Menu, {\n      role: menuRole,\n      renderOnMount: renderMenuOnMount,\n      rootCloseEvent: rootCloseEvent,\n      variant: menuVariant,\n      children: children\n    })]\n  });\n});\nNavDropdown.displayName = 'NavDropdown';\nexport default Object.assign(NavDropdown, {\n  Item: Dropdown.Item,\n  ItemText: Dropdown.ItemText,\n  Divider: Dropdown.Divider,\n  Header: Dropdown.Header\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,WAAW,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,CAAC;EACjDC,EAAE;EACFC,KAAK;EACLC,QAAQ;EACRC,QAAQ;EACRC,SAAS;EACTC,cAAc;EACdC,QAAQ;EACRC,QAAQ;EACRC,MAAM;EACNC,iBAAiB;EACjBC,WAAW;EACX,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT;EACA,MAAMC,aAAa,GAAGtB,kBAAkB,CAACuB,SAAS,EAAE,UAAU,CAAC;EAC/D,OAAO,aAAajB,KAAK,CAACL,QAAQ,EAAE;IAClCoB,GAAG,EAAEA,GAAG;IACR,GAAGD,KAAK;IACRP,SAAS,EAAEf,UAAU,CAACe,SAAS,EAAES,aAAa,CAAC;IAC/CX,QAAQ,EAAE,CAAC,aAAaP,IAAI,CAACH,QAAQ,CAACuB,MAAM,EAAE;MAC5Cf,EAAE,EAAEA,EAAE;MACNgB,QAAQ,EAAE,IAAI;MACdR,MAAM,EAAEA,MAAM;MACdD,QAAQ,EAAEA,QAAQ;MAClBU,aAAa,EAAEd,QAAQ;MACvBe,EAAE,EAAEzB,OAAO;MACXS,QAAQ,EAAED;IACZ,CAAC,CAAC,EAAE,aAAaN,IAAI,CAACH,QAAQ,CAAC2B,IAAI,EAAE;MACnCC,IAAI,EAAEd,QAAQ;MACde,aAAa,EAAEZ,iBAAiB;MAChCJ,cAAc,EAAEA,cAAc;MAC9BiB,OAAO,EAAEZ,WAAW;MACpBR,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFJ,WAAW,CAACyB,WAAW,GAAG,aAAa;AACvC,eAAeC,MAAM,CAACC,MAAM,CAAC3B,WAAW,EAAE;EACxC4B,IAAI,EAAElC,QAAQ,CAACkC,IAAI;EACnBC,QAAQ,EAAEnC,QAAQ,CAACmC,QAAQ;EAC3BC,OAAO,EAAEpC,QAAQ,CAACoC,OAAO;EACzBC,MAAM,EAAErC,QAAQ,CAACqC;AACnB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}