{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nimport Anchor from '@restart/ui/Anchor';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst PageItem = /*#__PURE__*/React.forwardRef(({\n  active = false,\n  disabled = false,\n  className,\n  style,\n  activeLabel = '(current)',\n  children,\n  linkStyle,\n  linkClassName,\n  as = Anchor,\n  ...props\n}, ref) => {\n  const Component = active || disabled ? 'span' : as;\n  return /*#__PURE__*/_jsx(\"li\", {\n    ref: ref,\n    style: style,\n    className: classNames(className, 'page-item', {\n      active,\n      disabled\n    }),\n    children: /*#__PURE__*/_jsxs(Component, {\n      className: classNames('page-link', linkClassName),\n      style: linkStyle,\n      ...props,\n      children: [children, active && activeLabel && /*#__PURE__*/_jsx(\"span\", {\n        className: \"visually-hidden\",\n        children: activeLabel\n      })]\n    })\n  });\n});\nPageItem.displayName = 'PageItem';\nexport default PageItem;\nfunction createButton(name, defaultValue, label = name) {\n  const Button = /*#__PURE__*/React.forwardRef(({\n    children,\n    ...props\n  }, ref) => /*#__PURE__*/_jsxs(PageItem, {\n    ...props,\n    ref: ref,\n    children: [/*#__PURE__*/_jsx(\"span\", {\n      \"aria-hidden\": \"true\",\n      children: children || defaultValue\n    }), /*#__PURE__*/_jsx(\"span\", {\n      className: \"visually-hidden\",\n      children: label\n    })]\n  }));\n  Button.displayName = name;\n  return Button;\n}\nexport const First = createButton('First', '«');\nexport const Prev = createButton('Prev', '‹', 'Previous');\nexport const Ellipsis = createButton('Ellipsis', '…', 'More');\nexport const Next = createButton('Next', '›');\nexport const Last = createButton('Last', '»');", "map": {"version": 3, "names": ["classNames", "React", "<PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "PageItem", "forwardRef", "active", "disabled", "className", "style", "activeLabel", "children", "linkStyle", "linkClassName", "as", "props", "ref", "Component", "displayName", "createButton", "name", "defaultValue", "label", "<PERSON><PERSON>", "First", "Prev", "El<PERSON><PERSON>", "Next", "Last"], "sources": ["/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/node_modules/react-bootstrap/esm/PageItem.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nimport Anchor from '@restart/ui/Anchor';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst PageItem = /*#__PURE__*/React.forwardRef(({\n  active = false,\n  disabled = false,\n  className,\n  style,\n  activeLabel = '(current)',\n  children,\n  linkStyle,\n  linkClassName,\n  as = Anchor,\n  ...props\n}, ref) => {\n  const Component = active || disabled ? 'span' : as;\n  return /*#__PURE__*/_jsx(\"li\", {\n    ref: ref,\n    style: style,\n    className: classNames(className, 'page-item', {\n      active,\n      disabled\n    }),\n    children: /*#__PURE__*/_jsxs(Component, {\n      className: classNames('page-link', linkClassName),\n      style: linkStyle,\n      ...props,\n      children: [children, active && activeLabel && /*#__PURE__*/_jsx(\"span\", {\n        className: \"visually-hidden\",\n        children: activeLabel\n      })]\n    })\n  });\n});\nPageItem.displayName = 'PageItem';\nexport default PageItem;\nfunction createButton(name, defaultValue, label = name) {\n  const Button = /*#__PURE__*/React.forwardRef(({\n    children,\n    ...props\n  }, ref) => /*#__PURE__*/_jsxs(PageItem, {\n    ...props,\n    ref: ref,\n    children: [/*#__PURE__*/_jsx(\"span\", {\n      \"aria-hidden\": \"true\",\n      children: children || defaultValue\n    }), /*#__PURE__*/_jsx(\"span\", {\n      className: \"visually-hidden\",\n      children: label\n    })]\n  }));\n  Button.displayName = name;\n  return Button;\n}\nexport const First = createButton('First', '«');\nexport const Prev = createButton('Prev', '‹', 'Previous');\nexport const Ellipsis = createButton('Ellipsis', '…', 'More');\nexport const Next = createButton('Next', '›');\nexport const Last = createButton('Last', '»');"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,oBAAoB;AACvC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,QAAQ,GAAG,aAAaN,KAAK,CAACO,UAAU,CAAC,CAAC;EAC9CC,MAAM,GAAG,KAAK;EACdC,QAAQ,GAAG,KAAK;EAChBC,SAAS;EACTC,KAAK;EACLC,WAAW,GAAG,WAAW;EACzBC,QAAQ;EACRC,SAAS;EACTC,aAAa;EACbC,EAAE,GAAGf,MAAM;EACX,GAAGgB;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,SAAS,GAAGX,MAAM,IAAIC,QAAQ,GAAG,MAAM,GAAGO,EAAE;EAClD,OAAO,aAAab,IAAI,CAAC,IAAI,EAAE;IAC7Be,GAAG,EAAEA,GAAG;IACRP,KAAK,EAAEA,KAAK;IACZD,SAAS,EAAEX,UAAU,CAACW,SAAS,EAAE,WAAW,EAAE;MAC5CF,MAAM;MACNC;IACF,CAAC,CAAC;IACFI,QAAQ,EAAE,aAAaR,KAAK,CAACc,SAAS,EAAE;MACtCT,SAAS,EAAEX,UAAU,CAAC,WAAW,EAAEgB,aAAa,CAAC;MACjDJ,KAAK,EAAEG,SAAS;MAChB,GAAGG,KAAK;MACRJ,QAAQ,EAAE,CAACA,QAAQ,EAAEL,MAAM,IAAII,WAAW,IAAI,aAAaT,IAAI,CAAC,MAAM,EAAE;QACtEO,SAAS,EAAE,iBAAiB;QAC5BG,QAAQ,EAAED;MACZ,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFN,QAAQ,CAACc,WAAW,GAAG,UAAU;AACjC,eAAed,QAAQ;AACvB,SAASe,YAAYA,CAACC,IAAI,EAAEC,YAAY,EAAEC,KAAK,GAAGF,IAAI,EAAE;EACtD,MAAMG,MAAM,GAAG,aAAazB,KAAK,CAACO,UAAU,CAAC,CAAC;IAC5CM,QAAQ;IACR,GAAGI;EACL,CAAC,EAAEC,GAAG,KAAK,aAAab,KAAK,CAACC,QAAQ,EAAE;IACtC,GAAGW,KAAK;IACRC,GAAG,EAAEA,GAAG;IACRL,QAAQ,EAAE,CAAC,aAAaV,IAAI,CAAC,MAAM,EAAE;MACnC,aAAa,EAAE,MAAM;MACrBU,QAAQ,EAAEA,QAAQ,IAAIU;IACxB,CAAC,CAAC,EAAE,aAAapB,IAAI,CAAC,MAAM,EAAE;MAC5BO,SAAS,EAAE,iBAAiB;MAC5BG,QAAQ,EAAEW;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;EACHC,MAAM,CAACL,WAAW,GAAGE,IAAI;EACzB,OAAOG,MAAM;AACf;AACA,OAAO,MAAMC,KAAK,GAAGL,YAAY,CAAC,OAAO,EAAE,GAAG,CAAC;AAC/C,OAAO,MAAMM,IAAI,GAAGN,YAAY,CAAC,MAAM,EAAE,GAAG,EAAE,UAAU,CAAC;AACzD,OAAO,MAAMO,QAAQ,GAAGP,YAAY,CAAC,UAAU,EAAE,GAAG,EAAE,MAAM,CAAC;AAC7D,OAAO,MAAMQ,IAAI,GAAGR,YAAY,CAAC,MAAM,EAAE,GAAG,CAAC;AAC7C,OAAO,MAAMS,IAAI,GAAGT,YAAY,CAAC,MAAM,EAAE,GAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}