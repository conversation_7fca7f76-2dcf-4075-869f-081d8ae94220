{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Badge = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  bg = 'primary',\n  pill = false,\n  text,\n  className,\n  as: Component = 'span',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'badge');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, pill && `rounded-pill`, text && `text-${text}`, bg && `bg-${bg}`)\n  });\n});\nBadge.displayName = 'Badge';\nexport default Badge;", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "jsx", "_jsx", "Badge", "forwardRef", "bsPrefix", "bg", "pill", "text", "className", "as", "Component", "props", "ref", "prefix", "displayName"], "sources": ["/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/node_modules/react-bootstrap/esm/Badge.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Badge = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  bg = 'primary',\n  pill = false,\n  text,\n  className,\n  as: Component = 'span',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'badge');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, pill && `rounded-pill`, text && `text-${text}`, bg && `bg-${bg}`)\n  });\n});\nBadge.displayName = 'Badge';\nexport default Badge;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,KAAK,GAAG,aAAaJ,KAAK,CAACK,UAAU,CAAC,CAAC;EAC3CC,QAAQ;EACRC,EAAE,GAAG,SAAS;EACdC,IAAI,GAAG,KAAK;EACZC,IAAI;EACJC,SAAS;EACTC,EAAE,EAAEC,SAAS,GAAG,MAAM;EACtB,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,MAAM,GAAGd,kBAAkB,CAACK,QAAQ,EAAE,OAAO,CAAC;EACpD,OAAO,aAAaH,IAAI,CAACS,SAAS,EAAE;IAClCE,GAAG,EAAEA,GAAG;IACR,GAAGD,KAAK;IACRH,SAAS,EAAEX,UAAU,CAACW,SAAS,EAAEK,MAAM,EAAEP,IAAI,IAAI,cAAc,EAAEC,IAAI,IAAI,QAAQA,IAAI,EAAE,EAAEF,EAAE,IAAI,MAAMA,EAAE,EAAE;EAC3G,CAAC,CAAC;AACJ,CAAC,CAAC;AACFH,KAAK,CAACY,WAAW,GAAG,OAAO;AAC3B,eAAeZ,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}