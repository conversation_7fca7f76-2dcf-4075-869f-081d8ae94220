{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport BreadcrumbItem from './BreadcrumbItem';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Breadcrumb = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  listProps = {},\n  children,\n  label = 'breadcrumb',\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'nav',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'breadcrumb');\n  return /*#__PURE__*/_jsx(Component, {\n    \"aria-label\": label,\n    className: className,\n    ref: ref,\n    ...props,\n    children: /*#__PURE__*/_jsx(\"ol\", {\n      ...listProps,\n      className: classNames(prefix, listProps == null ? void 0 : listProps.className),\n      children: children\n    })\n  });\n});\nBreadcrumb.displayName = 'Breadcrumb';\nexport default Object.assign(Breadcrumb, {\n  Item: BreadcrumbItem\n});", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "BreadcrumbItem", "jsx", "_jsx", "Breadcrumb", "forwardRef", "bsPrefix", "className", "listProps", "children", "label", "as", "Component", "props", "ref", "prefix", "displayName", "Object", "assign", "<PERSON><PERSON>"], "sources": ["/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/node_modules/react-bootstrap/esm/Breadcrumb.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport BreadcrumbItem from './BreadcrumbItem';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Breadcrumb = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  listProps = {},\n  children,\n  label = 'breadcrumb',\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'nav',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'breadcrumb');\n  return /*#__PURE__*/_jsx(Component, {\n    \"aria-label\": label,\n    className: className,\n    ref: ref,\n    ...props,\n    children: /*#__PURE__*/_jsx(\"ol\", {\n      ...listProps,\n      className: classNames(prefix, listProps == null ? void 0 : listProps.className),\n      children: children\n    })\n  });\n});\nBreadcrumb.displayName = 'Breadcrumb';\nexport default Object.assign(Breadcrumb, {\n  Item: BreadcrumbItem\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,UAAU,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAAC;EAChDC,QAAQ;EACRC,SAAS;EACTC,SAAS,GAAG,CAAC,CAAC;EACdC,QAAQ;EACRC,KAAK,GAAG,YAAY;EACpB;EACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;EACrB,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,MAAM,GAAGf,kBAAkB,CAACM,QAAQ,EAAE,YAAY,CAAC;EACzD,OAAO,aAAaH,IAAI,CAACS,SAAS,EAAE;IAClC,YAAY,EAAEF,KAAK;IACnBH,SAAS,EAAEA,SAAS;IACpBO,GAAG,EAAEA,GAAG;IACR,GAAGD,KAAK;IACRJ,QAAQ,EAAE,aAAaN,IAAI,CAAC,IAAI,EAAE;MAChC,GAAGK,SAAS;MACZD,SAAS,EAAET,UAAU,CAACiB,MAAM,EAAEP,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACD,SAAS,CAAC;MAC/EE,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFL,UAAU,CAACY,WAAW,GAAG,YAAY;AACrC,eAAeC,MAAM,CAACC,MAAM,CAACd,UAAU,EAAE;EACvCe,IAAI,EAAElB;AACR,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}