{"ast": null, "code": "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nexport var AccessibilityManager = /*#__PURE__*/function () {\n  function AccessibilityManager() {\n    _classCallCheck(this, AccessibilityManager);\n    _defineProperty(this, \"activeIndex\", 0);\n    _defineProperty(this, \"coordinateList\", []);\n    _defineProperty(this, \"layout\", 'horizontal');\n  }\n  return _createClass(AccessibilityManager, [{\n    key: \"setDetails\",\n    value: function setDetails(_ref) {\n      var _ref2;\n      var _ref$coordinateList = _ref.coordinateList,\n        coordinateList = _ref$coordinateList === void 0 ? null : _ref$coordinateList,\n        _ref$container = _ref.container,\n        container = _ref$container === void 0 ? null : _ref$container,\n        _ref$layout = _ref.layout,\n        layout = _ref$layout === void 0 ? null : _ref$layout,\n        _ref$offset = _ref.offset,\n        offset = _ref$offset === void 0 ? null : _ref$offset,\n        _ref$mouseHandlerCall = _ref.mouseHandlerCallback,\n        mouseHandlerCallback = _ref$mouseHandlerCall === void 0 ? null : _ref$mouseHandlerCall;\n      this.coordinateList = (_ref2 = coordinateList !== null && coordinateList !== void 0 ? coordinateList : this.coordinateList) !== null && _ref2 !== void 0 ? _ref2 : [];\n      this.container = container !== null && container !== void 0 ? container : this.container;\n      this.layout = layout !== null && layout !== void 0 ? layout : this.layout;\n      this.offset = offset !== null && offset !== void 0 ? offset : this.offset;\n      this.mouseHandlerCallback = mouseHandlerCallback !== null && mouseHandlerCallback !== void 0 ? mouseHandlerCallback : this.mouseHandlerCallback;\n\n      // Keep activeIndex in the bounds between 0 and the last coordinate index\n      this.activeIndex = Math.min(Math.max(this.activeIndex, 0), this.coordinateList.length - 1);\n    }\n  }, {\n    key: \"focus\",\n    value: function focus() {\n      this.spoofMouse();\n    }\n  }, {\n    key: \"keyboardEvent\",\n    value: function keyboardEvent(e) {\n      // The AccessibilityManager relies on the Tooltip component. When tooltips suddenly stop existing,\n      // it can cause errors. We use this function to check. We don't want arrow keys to be processed\n      // if there are no tooltips, since that will cause unexpected behavior of users.\n      if (this.coordinateList.length === 0) {\n        return;\n      }\n      switch (e.key) {\n        case 'ArrowRight':\n          {\n            if (this.layout !== 'horizontal') {\n              return;\n            }\n            this.activeIndex = Math.min(this.activeIndex + 1, this.coordinateList.length - 1);\n            this.spoofMouse();\n            break;\n          }\n        case 'ArrowLeft':\n          {\n            if (this.layout !== 'horizontal') {\n              return;\n            }\n            this.activeIndex = Math.max(this.activeIndex - 1, 0);\n            this.spoofMouse();\n            break;\n          }\n        default:\n          {\n            break;\n          }\n      }\n    }\n  }, {\n    key: \"setIndex\",\n    value: function setIndex(newIndex) {\n      this.activeIndex = newIndex;\n    }\n  }, {\n    key: \"spoofMouse\",\n    value: function spoofMouse() {\n      var _window, _window2;\n      if (this.layout !== 'horizontal') {\n        return;\n      }\n\n      // This can happen when the tooltips suddenly stop existing as children of the component\n      // That update doesn't otherwise fire events, so we have to double check here.\n      if (this.coordinateList.length === 0) {\n        return;\n      }\n      var _this$container$getBo = this.container.getBoundingClientRect(),\n        x = _this$container$getBo.x,\n        y = _this$container$getBo.y,\n        height = _this$container$getBo.height;\n      var coordinate = this.coordinateList[this.activeIndex].coordinate;\n      var scrollOffsetX = ((_window = window) === null || _window === void 0 ? void 0 : _window.scrollX) || 0;\n      var scrollOffsetY = ((_window2 = window) === null || _window2 === void 0 ? void 0 : _window2.scrollY) || 0;\n      var pageX = x + coordinate + scrollOffsetX;\n      var pageY = y + this.offset.top + height / 2 + scrollOffsetY;\n      this.mouseHandlerCallback({\n        pageX: pageX,\n        pageY: pageY\n      });\n    }\n  }]);\n}();", "map": {"version": 3, "names": ["_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "protoProps", "staticProps", "_defineProperty", "obj", "value", "t", "_toPrimitive", "r", "e", "toPrimitive", "call", "String", "Number", "AccessibilityManager", "setDetails", "_ref", "_ref2", "_ref$coordinateList", "coordinateList", "_ref$container", "container", "_ref$layout", "layout", "_ref$offset", "offset", "_ref$mouseHandlerCall", "mouseHandlerCallback", "activeIndex", "Math", "min", "max", "focus", "spoofMouse", "keyboardEvent", "setIndex", "newIndex", "_window", "_window2", "_this$container$getBo", "getBoundingClientRect", "x", "y", "height", "coordinate", "scrollOffsetX", "window", "scrollX", "scrollOffsetY", "scrollY", "pageX", "pageY", "top"], "sources": ["/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/node_modules/recharts/es6/chart/AccessibilityManager.js"], "sourcesContent": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, _toPropertyKey(descriptor.key), descriptor); } }\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nexport var AccessibilityManager = /*#__PURE__*/function () {\n  function AccessibilityManager() {\n    _classCallCheck(this, AccessibilityManager);\n    _defineProperty(this, \"activeIndex\", 0);\n    _defineProperty(this, \"coordinateList\", []);\n    _defineProperty(this, \"layout\", 'horizontal');\n  }\n  return _createClass(AccessibilityManager, [{\n    key: \"setDetails\",\n    value: function setDetails(_ref) {\n      var _ref2;\n      var _ref$coordinateList = _ref.coordinateList,\n        coordinateList = _ref$coordinateList === void 0 ? null : _ref$coordinateList,\n        _ref$container = _ref.container,\n        container = _ref$container === void 0 ? null : _ref$container,\n        _ref$layout = _ref.layout,\n        layout = _ref$layout === void 0 ? null : _ref$layout,\n        _ref$offset = _ref.offset,\n        offset = _ref$offset === void 0 ? null : _ref$offset,\n        _ref$mouseHandlerCall = _ref.mouseHandlerCallback,\n        mouseHandlerCallback = _ref$mouseHandlerCall === void 0 ? null : _ref$mouseHandlerCall;\n      this.coordinateList = (_ref2 = coordinateList !== null && coordinateList !== void 0 ? coordinateList : this.coordinateList) !== null && _ref2 !== void 0 ? _ref2 : [];\n      this.container = container !== null && container !== void 0 ? container : this.container;\n      this.layout = layout !== null && layout !== void 0 ? layout : this.layout;\n      this.offset = offset !== null && offset !== void 0 ? offset : this.offset;\n      this.mouseHandlerCallback = mouseHandlerCallback !== null && mouseHandlerCallback !== void 0 ? mouseHandlerCallback : this.mouseHandlerCallback;\n\n      // Keep activeIndex in the bounds between 0 and the last coordinate index\n      this.activeIndex = Math.min(Math.max(this.activeIndex, 0), this.coordinateList.length - 1);\n    }\n  }, {\n    key: \"focus\",\n    value: function focus() {\n      this.spoofMouse();\n    }\n  }, {\n    key: \"keyboardEvent\",\n    value: function keyboardEvent(e) {\n      // The AccessibilityManager relies on the Tooltip component. When tooltips suddenly stop existing,\n      // it can cause errors. We use this function to check. We don't want arrow keys to be processed\n      // if there are no tooltips, since that will cause unexpected behavior of users.\n      if (this.coordinateList.length === 0) {\n        return;\n      }\n      switch (e.key) {\n        case 'ArrowRight':\n          {\n            if (this.layout !== 'horizontal') {\n              return;\n            }\n            this.activeIndex = Math.min(this.activeIndex + 1, this.coordinateList.length - 1);\n            this.spoofMouse();\n            break;\n          }\n        case 'ArrowLeft':\n          {\n            if (this.layout !== 'horizontal') {\n              return;\n            }\n            this.activeIndex = Math.max(this.activeIndex - 1, 0);\n            this.spoofMouse();\n            break;\n          }\n        default:\n          {\n            break;\n          }\n      }\n    }\n  }, {\n    key: \"setIndex\",\n    value: function setIndex(newIndex) {\n      this.activeIndex = newIndex;\n    }\n  }, {\n    key: \"spoofMouse\",\n    value: function spoofMouse() {\n      var _window, _window2;\n      if (this.layout !== 'horizontal') {\n        return;\n      }\n\n      // This can happen when the tooltips suddenly stop existing as children of the component\n      // That update doesn't otherwise fire events, so we have to double check here.\n      if (this.coordinateList.length === 0) {\n        return;\n      }\n      var _this$container$getBo = this.container.getBoundingClientRect(),\n        x = _this$container$getBo.x,\n        y = _this$container$getBo.y,\n        height = _this$container$getBo.height;\n      var coordinate = this.coordinateList[this.activeIndex].coordinate;\n      var scrollOffsetX = ((_window = window) === null || _window === void 0 ? void 0 : _window.scrollX) || 0;\n      var scrollOffsetY = ((_window2 = window) === null || _window2 === void 0 ? void 0 : _window2.scrollY) || 0;\n      var pageX = x + coordinate + scrollOffsetX;\n      var pageY = y + this.offset.top + height / 2 + scrollOffsetY;\n      this.mouseHandlerCallback({\n        pageX: pageX,\n        pageY: pageY\n      });\n    }\n  }]);\n}();"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AACxJ,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;IAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;IAAED,UAAU,CAACE,YAAY,GAAG,IAAI;IAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;IAAEC,MAAM,CAACC,cAAc,CAACT,MAAM,EAAEU,cAAc,CAACN,UAAU,CAACO,GAAG,CAAC,EAAEP,UAAU,CAAC;EAAE;AAAE;AAC5U,SAASQ,YAAYA,CAACf,WAAW,EAAEgB,UAAU,EAAEC,WAAW,EAAE;EAAE,IAAID,UAAU,EAAEd,iBAAiB,CAACF,WAAW,CAACH,SAAS,EAAEmB,UAAU,CAAC;EAAE,IAAIC,WAAW,EAAEf,iBAAiB,CAACF,WAAW,EAAEiB,WAAW,CAAC;EAAEN,MAAM,CAACC,cAAc,CAACZ,WAAW,EAAE,WAAW,EAAE;IAAEU,QAAQ,EAAE;EAAM,CAAC,CAAC;EAAE,OAAOV,WAAW;AAAE;AAC5R,SAASkB,eAAeA,CAACC,GAAG,EAAEL,GAAG,EAAEM,KAAK,EAAE;EAAEN,GAAG,GAAGD,cAAc,CAACC,GAAG,CAAC;EAAE,IAAIA,GAAG,IAAIK,GAAG,EAAE;IAAER,MAAM,CAACC,cAAc,CAACO,GAAG,EAAEL,GAAG,EAAE;MAAEM,KAAK,EAAEA,KAAK;MAAEZ,UAAU,EAAE,IAAI;MAAEC,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EAAE,CAAC,MAAM;IAAES,GAAG,CAACL,GAAG,CAAC,GAAGM,KAAK;EAAE;EAAE,OAAOD,GAAG;AAAE;AAC3O,SAASN,cAAcA,CAACQ,CAAC,EAAE;EAAE,IAAIhB,CAAC,GAAGiB,YAAY,CAACD,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAI7B,OAAO,CAACa,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASiB,YAAYA,CAACD,CAAC,EAAEE,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAI/B,OAAO,CAAC6B,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIG,CAAC,GAAGH,CAAC,CAAC3B,MAAM,CAAC+B,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKD,CAAC,EAAE;IAAE,IAAInB,CAAC,GAAGmB,CAAC,CAACE,IAAI,CAACL,CAAC,EAAEE,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAI/B,OAAO,CAACa,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIJ,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKsB,CAAC,GAAGI,MAAM,GAAGC,MAAM,EAAEP,CAAC,CAAC;AAAE;AAC3T,OAAO,IAAIQ,oBAAoB,GAAG,aAAa,YAAY;EACzD,SAASA,oBAAoBA,CAAA,EAAG;IAC9B/B,eAAe,CAAC,IAAI,EAAE+B,oBAAoB,CAAC;IAC3CX,eAAe,CAAC,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;IACvCA,eAAe,CAAC,IAAI,EAAE,gBAAgB,EAAE,EAAE,CAAC;IAC3CA,eAAe,CAAC,IAAI,EAAE,QAAQ,EAAE,YAAY,CAAC;EAC/C;EACA,OAAOH,YAAY,CAACc,oBAAoB,EAAE,CAAC;IACzCf,GAAG,EAAE,YAAY;IACjBM,KAAK,EAAE,SAASU,UAAUA,CAACC,IAAI,EAAE;MAC/B,IAAIC,KAAK;MACT,IAAIC,mBAAmB,GAAGF,IAAI,CAACG,cAAc;QAC3CA,cAAc,GAAGD,mBAAmB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,mBAAmB;QAC5EE,cAAc,GAAGJ,IAAI,CAACK,SAAS;QAC/BA,SAAS,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,cAAc;QAC7DE,WAAW,GAAGN,IAAI,CAACO,MAAM;QACzBA,MAAM,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,WAAW;QACpDE,WAAW,GAAGR,IAAI,CAACS,MAAM;QACzBA,MAAM,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,WAAW;QACpDE,qBAAqB,GAAGV,IAAI,CAACW,oBAAoB;QACjDA,oBAAoB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;MACxF,IAAI,CAACP,cAAc,GAAG,CAACF,KAAK,GAAGE,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAG,IAAI,CAACA,cAAc,MAAM,IAAI,IAAIF,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,EAAE;MACrK,IAAI,CAACI,SAAS,GAAGA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAG,IAAI,CAACA,SAAS;MACxF,IAAI,CAACE,MAAM,GAAGA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAG,IAAI,CAACA,MAAM;MACzE,IAAI,CAACE,MAAM,GAAGA,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAGA,MAAM,GAAG,IAAI,CAACA,MAAM;MACzE,IAAI,CAACE,oBAAoB,GAAGA,oBAAoB,KAAK,IAAI,IAAIA,oBAAoB,KAAK,KAAK,CAAC,GAAGA,oBAAoB,GAAG,IAAI,CAACA,oBAAoB;;MAE/I;MACA,IAAI,CAACC,WAAW,GAAGC,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAAC,IAAI,CAACH,WAAW,EAAE,CAAC,CAAC,EAAE,IAAI,CAACT,cAAc,CAAC5B,MAAM,GAAG,CAAC,CAAC;IAC5F;EACF,CAAC,EAAE;IACDQ,GAAG,EAAE,OAAO;IACZM,KAAK,EAAE,SAAS2B,KAAKA,CAAA,EAAG;MACtB,IAAI,CAACC,UAAU,CAAC,CAAC;IACnB;EACF,CAAC,EAAE;IACDlC,GAAG,EAAE,eAAe;IACpBM,KAAK,EAAE,SAAS6B,aAAaA,CAACzB,CAAC,EAAE;MAC/B;MACA;MACA;MACA,IAAI,IAAI,CAACU,cAAc,CAAC5B,MAAM,KAAK,CAAC,EAAE;QACpC;MACF;MACA,QAAQkB,CAAC,CAACV,GAAG;QACX,KAAK,YAAY;UACf;YACE,IAAI,IAAI,CAACwB,MAAM,KAAK,YAAY,EAAE;cAChC;YACF;YACA,IAAI,CAACK,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACF,WAAW,GAAG,CAAC,EAAE,IAAI,CAACT,cAAc,CAAC5B,MAAM,GAAG,CAAC,CAAC;YACjF,IAAI,CAAC0C,UAAU,CAAC,CAAC;YACjB;UACF;QACF,KAAK,WAAW;UACd;YACE,IAAI,IAAI,CAACV,MAAM,KAAK,YAAY,EAAE;cAChC;YACF;YACA,IAAI,CAACK,WAAW,GAAGC,IAAI,CAACE,GAAG,CAAC,IAAI,CAACH,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC;YACpD,IAAI,CAACK,UAAU,CAAC,CAAC;YACjB;UACF;QACF;UACE;YACE;UACF;MACJ;IACF;EACF,CAAC,EAAE;IACDlC,GAAG,EAAE,UAAU;IACfM,KAAK,EAAE,SAAS8B,QAAQA,CAACC,QAAQ,EAAE;MACjC,IAAI,CAACR,WAAW,GAAGQ,QAAQ;IAC7B;EACF,CAAC,EAAE;IACDrC,GAAG,EAAE,YAAY;IACjBM,KAAK,EAAE,SAAS4B,UAAUA,CAAA,EAAG;MAC3B,IAAII,OAAO,EAAEC,QAAQ;MACrB,IAAI,IAAI,CAACf,MAAM,KAAK,YAAY,EAAE;QAChC;MACF;;MAEA;MACA;MACA,IAAI,IAAI,CAACJ,cAAc,CAAC5B,MAAM,KAAK,CAAC,EAAE;QACpC;MACF;MACA,IAAIgD,qBAAqB,GAAG,IAAI,CAAClB,SAAS,CAACmB,qBAAqB,CAAC,CAAC;QAChEC,CAAC,GAAGF,qBAAqB,CAACE,CAAC;QAC3BC,CAAC,GAAGH,qBAAqB,CAACG,CAAC;QAC3BC,MAAM,GAAGJ,qBAAqB,CAACI,MAAM;MACvC,IAAIC,UAAU,GAAG,IAAI,CAACzB,cAAc,CAAC,IAAI,CAACS,WAAW,CAAC,CAACgB,UAAU;MACjE,IAAIC,aAAa,GAAG,CAAC,CAACR,OAAO,GAAGS,MAAM,MAAM,IAAI,IAAIT,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACU,OAAO,KAAK,CAAC;MACvG,IAAIC,aAAa,GAAG,CAAC,CAACV,QAAQ,GAAGQ,MAAM,MAAM,IAAI,IAAIR,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACW,OAAO,KAAK,CAAC;MAC1G,IAAIC,KAAK,GAAGT,CAAC,GAAGG,UAAU,GAAGC,aAAa;MAC1C,IAAIM,KAAK,GAAGT,CAAC,GAAG,IAAI,CAACjB,MAAM,CAAC2B,GAAG,GAAGT,MAAM,GAAG,CAAC,GAAGK,aAAa;MAC5D,IAAI,CAACrB,oBAAoB,CAAC;QACxBuB,KAAK,EAAEA,KAAK;QACZC,KAAK,EAAEA;MACT,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}