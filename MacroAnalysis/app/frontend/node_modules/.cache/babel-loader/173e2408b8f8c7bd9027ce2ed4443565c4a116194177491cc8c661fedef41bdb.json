{"ast": null, "code": "import { SSRProvider } from '@restart/ui/ssr';\nexport default SSRProvider;", "map": {"version": 3, "names": ["SSRProvider"], "sources": ["/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/node_modules/react-bootstrap/esm/SSRProvider.js"], "sourcesContent": ["import { SSRProvider } from '@restart/ui/ssr';\nexport default SSRProvider;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,iBAAiB;AAC7C,eAAeA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}