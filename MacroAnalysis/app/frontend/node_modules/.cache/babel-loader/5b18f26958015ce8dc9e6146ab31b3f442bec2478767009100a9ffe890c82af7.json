{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport Offcanvas from './Offcanvas';\nimport NavbarContext from './NavbarContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst NavbarOffcanvas = /*#__PURE__*/React.forwardRef(({\n  onHide,\n  ...props\n}, ref) => {\n  const context = useContext(NavbarContext);\n  const handleHide = useEventCallback(() => {\n    context == null || context.onToggle == null || context.onToggle();\n    onHide == null || onHide();\n  });\n  return /*#__PURE__*/_jsx(Offcanvas, {\n    ref: ref,\n    show: !!(context != null && context.expanded),\n    ...props,\n    renderStaticNode: true,\n    onHide: handleHide\n  });\n});\nNavbarOffcanvas.displayName = 'NavbarOffcanvas';\nexport default NavbarOffcanvas;", "map": {"version": 3, "names": ["React", "useContext", "useEventCallback", "<PERSON><PERSON><PERSON>", "NavbarContext", "jsx", "_jsx", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardRef", "onHide", "props", "ref", "context", "handleHide", "onToggle", "show", "expanded", "renderStaticNode", "displayName"], "sources": ["/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/node_modules/react-bootstrap/esm/NavbarOffcanvas.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport { useContext } from 'react';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport Offcanvas from './Offcanvas';\nimport NavbarContext from './NavbarContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst NavbarOffcanvas = /*#__PURE__*/React.forwardRef(({\n  onHide,\n  ...props\n}, ref) => {\n  const context = useContext(NavbarContext);\n  const handleHide = useEventCallback(() => {\n    context == null || context.onToggle == null || context.onToggle();\n    onHide == null || onHide();\n  });\n  return /*#__PURE__*/_jsx(Offcanvas, {\n    ref: ref,\n    show: !!(context != null && context.expanded),\n    ...props,\n    renderStaticNode: true,\n    onHide: handleHide\n  });\n});\nNavbarOffcanvas.displayName = 'NavbarOffcanvas';\nexport default NavbarOffcanvas;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,OAAO;AAClC,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,eAAe,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,CAAC;EACrDC,MAAM;EACN,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,OAAO,GAAGX,UAAU,CAACG,aAAa,CAAC;EACzC,MAAMS,UAAU,GAAGX,gBAAgB,CAAC,MAAM;IACxCU,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACE,QAAQ,IAAI,IAAI,IAAIF,OAAO,CAACE,QAAQ,CAAC,CAAC;IACjEL,MAAM,IAAI,IAAI,IAAIA,MAAM,CAAC,CAAC;EAC5B,CAAC,CAAC;EACF,OAAO,aAAaH,IAAI,CAACH,SAAS,EAAE;IAClCQ,GAAG,EAAEA,GAAG;IACRI,IAAI,EAAE,CAAC,EAAEH,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACI,QAAQ,CAAC;IAC7C,GAAGN,KAAK;IACRO,gBAAgB,EAAE,IAAI;IACtBR,MAAM,EAAEI;EACV,CAAC,CAAC;AACJ,CAAC,CAAC;AACFN,eAAe,CAACW,WAAW,GAAG,iBAAiB;AAC/C,eAAeX,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}