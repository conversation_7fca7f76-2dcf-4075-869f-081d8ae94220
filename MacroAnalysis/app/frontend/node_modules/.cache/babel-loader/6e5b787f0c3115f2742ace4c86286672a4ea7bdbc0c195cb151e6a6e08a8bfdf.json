{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/pages/DailyFundamentals.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { futuresApi } from '../services/api';\nimport { Tabs, Tab } from 'react-bootstrap';\nimport ESFuturesChart from '../components/charts/ESFuturesChart';\nimport VIXFuturesChart from '../components/charts/VIXFuturesChart';\nimport TradingViewChartWrapper from '../components/charts/TradingViewChartWrapper';\nimport LoadingSpinner from '../components/layout/LoadingSpinner';\nimport ErrorAlert from '../components/layout/ErrorAlert';\nimport './DailyFundamentals.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DailyFundamentals = () => {\n  _s();\n  const [esData, setEsData] = useState([]);\n  const [vixData, setVixData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n  const [error, setError] = useState(null);\n  const [lastUpdated, setLastUpdated] = useState(null);\n  const [activeTab, setActiveTab] = useState('es');\n  const [chartType, setChartType] = useState('custom'); // 'custom' or 'tradingview'\n  const [chartSettings, setChartSettings] = useState({\n    showVolume: false,\n    showMovingAverages: false,\n    maShortPeriod: 20,\n    maLongPeriod: 50,\n    showRSI: false,\n    rsiPeriod: 14,\n    theme: 'light'\n  });\n  const fetchFuturesData = async () => {\n    setLoading(true);\n    try {\n      // Fetch ES futures data\n      const esResponse = await futuresApi.getESFutures();\n      setEsData(esResponse.data.data);\n\n      // Fetch VIX futures data\n      const vixResponse = await futuresApi.getVIXFutures();\n      setVixData(vixResponse.data.data);\n\n      // Set last updated timestamp\n      if (esResponse.data.last_updated) {\n        setLastUpdated(new Date(esResponse.data.last_updated * 1000).toLocaleString());\n      }\n      setError(null);\n    } catch (err) {\n      console.error('Error fetching futures data:', err);\n      setError('Failed to load futures data. Please try again later.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleRefreshData = async () => {\n    setRefreshing(true);\n    try {\n      await futuresApi.refreshFuturesData();\n      await fetchFuturesData();\n    } catch (err) {\n      console.error('Error refreshing futures data:', err);\n      setError('Failed to refresh futures data. Please try again later.');\n    } finally {\n      setRefreshing(false);\n    }\n  };\n  useEffect(() => {\n    fetchFuturesData();\n  }, []);\n  const handleTabChange = tab => {\n    setActiveTab(tab);\n  };\n  const handleChartSettingsChange = newSettings => {\n    setChartSettings(newSettings);\n  };\n  const handleChartTypeChange = type => {\n    setChartType(type);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n      message: \"Loading futures data...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 12\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(ErrorAlert, {\n      message: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"daily-fundamentals-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Daily Fundamentals\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"description\",\n        children: \"Track daily ES futures and VIX futures to monitor market sentiment and volatility trends.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"actions-container\",\n        children: [lastUpdated && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"last-updated\",\n          children: [\"Last updated: \", lastUpdated]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"chart-type-toggle\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: chartType === 'custom' ? 'active' : '',\n            onClick: () => handleChartTypeChange('custom'),\n            children: \"Custom Charts\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: chartType === 'tradingview' ? 'active' : '',\n            onClick: () => handleChartTypeChange('tradingview'),\n            children: \"TradingView\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `refresh-button ${refreshing ? 'refreshing' : ''}`,\n          onClick: handleRefreshData,\n          disabled: refreshing || loading,\n          children: refreshing ? 'Refreshing...' : 'Refresh Data'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"data-summary\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"summary-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"ES Futures\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"E-mini S&P 500 futures represent market expectations for the S&P 500 index.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), esData.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Latest Close:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: parseFloat(esData[esData.length - 1].close).toFixed(2)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Data Points:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: esData.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"summary-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"VIX Futures\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"VIX futures track expected market volatility over the coming months.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), vixData.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stats\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Latest Close:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: parseFloat(vixData[vixData.length - 1].close).toFixed(2)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"stat-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Data Points:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: vixData.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chart-container\",\n      children: chartType === 'custom' ? /*#__PURE__*/_jsxDEV(Tabs, {\n        activeKey: activeTab,\n        onSelect: handleTabChange,\n        id: \"futures-tabs\",\n        className: \"mb-3 futures-tabs\",\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          eventKey: \"es\",\n          title: \"ES Futures\",\n          children: esData.length > 0 ? /*#__PURE__*/_jsxDEV(ESFuturesChart, {\n            data: esData,\n            initialSettings: chartSettings,\n            onSettingsChange: handleChartSettingsChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No ES futures data available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          eventKey: \"vix\",\n          title: \"VIX Futures\",\n          children: vixData.length > 0 ? /*#__PURE__*/_jsxDEV(VIXFuturesChart, {\n            data: vixData,\n            initialSettings: chartSettings,\n            onSettingsChange: handleChartSettingsChange\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No VIX futures data available\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(TradingViewChartWrapper, {\n        symbol: activeTab === 'es' ? 'ES' : 'VIX',\n        initialSettings: chartSettings,\n        onSettingsChange: handleChartSettingsChange\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"data-insights\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Market Insights\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"insight-cards\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"insight-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"ES Futures Analysis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: esData.length > 0 ? `The ES futures have ${esData[esData.length - 1].close > esData[esData.length - 2].close ? 'risen' : 'fallen'} recently, indicating ${esData[esData.length - 1].close > esData[esData.length - 2].close ? 'positive' : 'negative'} market sentiment.` : 'Insufficient data for analysis.'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"insight-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"VIX Futures Analysis\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: vixData.length > 0 ? `The VIX futures are currently ${vixData[vixData.length - 1].close > 20 ? 'above 20, suggesting elevated market uncertainty' : 'below 20, suggesting relatively low expected volatility'}.` : 'Insufficient data for analysis.'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 93,\n    columnNumber: 5\n  }, this);\n};\n_s(DailyFundamentals, \"rneKn9RWR3WgdEtwi/mMGc4vl+8=\");\n_c = DailyFundamentals;\nexport default DailyFundamentals;\nvar _c;\n$RefreshReg$(_c, \"DailyFundamentals\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "futuresApi", "Tabs", "Tab", "ESFuturesChart", "VIXFuturesChart", "TradingViewChartWrapper", "LoadingSpinner", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "DailyFundamentals", "_s", "esData", "setEsData", "vixData", "setVixData", "loading", "setLoading", "refreshing", "setRefreshing", "error", "setError", "lastUpdated", "setLastUpdated", "activeTab", "setActiveTab", "chartType", "setChartType", "chartSettings", "setChartSettings", "showVolume", "showMovingAverages", "maShortPeriod", "maLongPeriod", "showRSI", "rsiPeriod", "theme", "fetchFuturesData", "esResponse", "getESFutures", "data", "vixResponse", "getVIXFutures", "last_updated", "Date", "toLocaleString", "err", "console", "handleRefreshData", "refreshFuturesData", "handleTabChange", "tab", "handleChartSettingsChange", "newSettings", "handleChartTypeChange", "type", "message", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "onClick", "disabled", "length", "parseFloat", "close", "toFixed", "active<PERSON><PERSON>", "onSelect", "id", "eventKey", "title", "initialSettings", "onSettingsChange", "symbol", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/pages/DailyFundamentals.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { futuresApi } from '../services/api';\nimport { Tabs, Tab } from 'react-bootstrap';\nimport ESFuturesChart from '../components/charts/ESFuturesChart';\nimport VIXFuturesChart from '../components/charts/VIXFuturesChart';\nimport TradingViewChartWrapper from '../components/charts/TradingViewChartWrapper';\nimport LoadingSpinner from '../components/layout/LoadingSpinner';\nimport ErrorAlert from '../components/layout/ErrorAlert';\nimport './DailyFundamentals.css';\n\nconst DailyFundamentals = () => {\n  const [esData, setEsData] = useState([]);\n  const [vixData, setVixData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [refreshing, setRefreshing] = useState(false);\n  const [error, setError] = useState(null);\n  const [lastUpdated, setLastUpdated] = useState(null);\n  const [activeTab, setActiveTab] = useState('es');\n  const [chartType, setChartType] = useState('custom'); // 'custom' or 'tradingview'\n  const [chartSettings, setChartSettings] = useState({\n    showVolume: false,\n    showMovingAverages: false,\n    maShortPeriod: 20,\n    maLongPeriod: 50,\n    showRSI: false,\n    rsiPeriod: 14,\n    theme: 'light'\n  });\n\n  const fetchFuturesData = async () => {\n    setLoading(true);\n    try {\n      // Fetch ES futures data\n      const esResponse = await futuresApi.getESFutures();\n      setEsData(esResponse.data.data);\n      \n      // Fetch VIX futures data\n      const vixResponse = await futuresApi.getVIXFutures();\n      setVixData(vixResponse.data.data);\n      \n      // Set last updated timestamp\n      if (esResponse.data.last_updated) {\n        setLastUpdated(new Date(esResponse.data.last_updated * 1000).toLocaleString());\n      }\n      \n      setError(null);\n    } catch (err) {\n      console.error('Error fetching futures data:', err);\n      setError('Failed to load futures data. Please try again later.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRefreshData = async () => {\n    setRefreshing(true);\n    try {\n      await futuresApi.refreshFuturesData();\n      await fetchFuturesData();\n    } catch (err) {\n      console.error('Error refreshing futures data:', err);\n      setError('Failed to refresh futures data. Please try again later.');\n    } finally {\n      setRefreshing(false);\n    }\n  };\n  \n  useEffect(() => {\n    fetchFuturesData();\n  }, []);\n\n  const handleTabChange = (tab) => {\n    setActiveTab(tab);\n  };\n  \n  const handleChartSettingsChange = (newSettings) => {\n    setChartSettings(newSettings);\n  };\n  \n  const handleChartTypeChange = (type) => {\n    setChartType(type);\n  };\n\n  if (loading) {\n    return <LoadingSpinner message=\"Loading futures data...\" />;\n  }\n\n  if (error) {\n    return <ErrorAlert message={error} />;\n  }\n\n  return (\n    <div className=\"daily-fundamentals-container\">\n      <h1>Daily Fundamentals</h1>\n      <div className=\"page-header-container\">\n        <p className=\"description\">\n          Track daily ES futures and VIX futures to monitor market sentiment and volatility trends.\n        </p>\n        <div className=\"actions-container\">\n          {lastUpdated && (\n            <span className=\"last-updated\">\n              Last updated: {lastUpdated}\n            </span>\n          )}\n          <div className=\"chart-type-toggle\">\n            <button \n              className={chartType === 'custom' ? 'active' : ''}\n              onClick={() => handleChartTypeChange('custom')}\n            >\n              Custom Charts\n            </button>\n            <button \n              className={chartType === 'tradingview' ? 'active' : ''}\n              onClick={() => handleChartTypeChange('tradingview')}\n            >\n              TradingView\n            </button>\n          </div>\n          <button \n            className={`refresh-button ${refreshing ? 'refreshing' : ''}`}\n            onClick={handleRefreshData}\n            disabled={refreshing || loading}\n          >\n            {refreshing ? 'Refreshing...' : 'Refresh Data'}\n          </button>\n        </div>\n      </div>\n      \n      <div className=\"data-summary\">\n        <div className=\"summary-card\">\n          <h3>ES Futures</h3>\n          <p>E-mini S&P 500 futures represent market expectations for the S&P 500 index.</p>\n          {esData.length > 0 && (\n            <div className=\"stats\">\n              <div className=\"stat-item\">\n                <span className=\"label\">Latest Close:</span>\n                <span className=\"value\">{parseFloat(esData[esData.length - 1].close).toFixed(2)}</span>\n              </div>\n              <div className=\"stat-item\">\n                <span className=\"label\">Data Points:</span>\n                <span className=\"value\">{esData.length}</span>\n              </div>\n            </div>\n          )}\n        </div>\n        \n        <div className=\"summary-card\">\n          <h3>VIX Futures</h3>\n          <p>VIX futures track expected market volatility over the coming months.</p>\n          {vixData.length > 0 && (\n            <div className=\"stats\">\n              <div className=\"stat-item\">\n                <span className=\"label\">Latest Close:</span>\n                <span className=\"value\">{parseFloat(vixData[vixData.length - 1].close).toFixed(2)}</span>\n              </div>\n              <div className=\"stat-item\">\n                <span className=\"label\">Data Points:</span>\n                <span className=\"value\">{vixData.length}</span>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n      \n      <div className=\"chart-container\">\n        {chartType === 'custom' ? (\n          <Tabs\n            activeKey={activeTab}\n            onSelect={handleTabChange}\n            id=\"futures-tabs\"\n            className=\"mb-3 futures-tabs\"\n          >\n            <Tab eventKey=\"es\" title=\"ES Futures\">\n              {esData.length > 0 ? (\n                <ESFuturesChart \n                  data={esData} \n                  initialSettings={chartSettings}\n                  onSettingsChange={handleChartSettingsChange}\n                />\n              ) : (\n                <p>No ES futures data available</p>\n              )}\n            </Tab>\n            <Tab eventKey=\"vix\" title=\"VIX Futures\">\n              {vixData.length > 0 ? (\n                <VIXFuturesChart \n                  data={vixData} \n                  initialSettings={chartSettings}\n                  onSettingsChange={handleChartSettingsChange}\n                />\n              ) : (\n                <p>No VIX futures data available</p>\n              )}\n            </Tab>\n          </Tabs>\n        ) : (\n          <TradingViewChartWrapper \n            symbol={activeTab === 'es' ? 'ES' : 'VIX'}\n            initialSettings={chartSettings}\n            onSettingsChange={handleChartSettingsChange}\n          />\n        )}\n      </div>\n      \n      <div className=\"data-insights\">\n        <h2>Market Insights</h2>\n        <div className=\"insight-cards\">\n          <div className=\"insight-card\">\n            <h3>ES Futures Analysis</h3>\n            <p>\n              {esData.length > 0 \n                ? `The ES futures have ${\n                    esData[esData.length - 1].close > esData[esData.length - 2].close \n                      ? 'risen' \n                      : 'fallen'\n                  } recently, indicating ${\n                    esData[esData.length - 1].close > esData[esData.length - 2].close \n                      ? 'positive' \n                      : 'negative'\n                  } market sentiment.`\n                : 'Insufficient data for analysis.'}\n            </p>\n          </div>\n          <div className=\"insight-card\">\n            <h3>VIX Futures Analysis</h3>\n            <p>\n              {vixData.length > 0 \n                ? `The VIX futures are currently ${\n                    vixData[vixData.length - 1].close > 20 \n                      ? 'above 20, suggesting elevated market uncertainty' \n                      : 'below 20, suggesting relatively low expected volatility'\n                  }.`\n                : 'Insufficient data for analysis.'}\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DailyFundamentals;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,QAAQ,iBAAiB;AAC5C,SAASC,IAAI,EAAEC,GAAG,QAAQ,iBAAiB;AAC3C,OAAOC,cAAc,MAAM,qCAAqC;AAChE,OAAOC,eAAe,MAAM,sCAAsC;AAClE,OAAOC,uBAAuB,MAAM,8CAA8C;AAClF,OAAOC,cAAc,MAAM,qCAAqC;AAChE,OAAOC,UAAU,MAAM,iCAAiC;AACxD,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACwB,WAAW,EAAEC,cAAc,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EACtD,MAAM,CAAC8B,aAAa,EAAEC,gBAAgB,CAAC,GAAG/B,QAAQ,CAAC;IACjDgC,UAAU,EAAE,KAAK;IACjBC,kBAAkB,EAAE,KAAK;IACzBC,aAAa,EAAE,EAAE;IACjBC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnCpB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAMqB,UAAU,GAAG,MAAMtC,UAAU,CAACuC,YAAY,CAAC,CAAC;MAClD1B,SAAS,CAACyB,UAAU,CAACE,IAAI,CAACA,IAAI,CAAC;;MAE/B;MACA,MAAMC,WAAW,GAAG,MAAMzC,UAAU,CAAC0C,aAAa,CAAC,CAAC;MACpD3B,UAAU,CAAC0B,WAAW,CAACD,IAAI,CAACA,IAAI,CAAC;;MAEjC;MACA,IAAIF,UAAU,CAACE,IAAI,CAACG,YAAY,EAAE;QAChCpB,cAAc,CAAC,IAAIqB,IAAI,CAACN,UAAU,CAACE,IAAI,CAACG,YAAY,GAAG,IAAI,CAAC,CAACE,cAAc,CAAC,CAAC,CAAC;MAChF;MAEAxB,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOyB,GAAG,EAAE;MACZC,OAAO,CAAC3B,KAAK,CAAC,8BAA8B,EAAE0B,GAAG,CAAC;MAClDzB,QAAQ,CAAC,sDAAsD,CAAC;IAClE,CAAC,SAAS;MACRJ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC7B,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMnB,UAAU,CAACiD,kBAAkB,CAAC,CAAC;MACrC,MAAMZ,gBAAgB,CAAC,CAAC;IAC1B,CAAC,CAAC,OAAOS,GAAG,EAAE;MACZC,OAAO,CAAC3B,KAAK,CAAC,gCAAgC,EAAE0B,GAAG,CAAC;MACpDzB,QAAQ,CAAC,yDAAyD,CAAC;IACrE,CAAC,SAAS;MACRF,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAEDpB,SAAS,CAAC,MAAM;IACdsC,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMa,eAAe,GAAIC,GAAG,IAAK;IAC/B1B,YAAY,CAAC0B,GAAG,CAAC;EACnB,CAAC;EAED,MAAMC,yBAAyB,GAAIC,WAAW,IAAK;IACjDxB,gBAAgB,CAACwB,WAAW,CAAC;EAC/B,CAAC;EAED,MAAMC,qBAAqB,GAAIC,IAAI,IAAK;IACtC5B,YAAY,CAAC4B,IAAI,CAAC;EACpB,CAAC;EAED,IAAIvC,OAAO,EAAE;IACX,oBAAOP,OAAA,CAACH,cAAc;MAACkD,OAAO,EAAC;IAAyB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAC7D;EAEA,IAAIxC,KAAK,EAAE;IACT,oBAAOX,OAAA,CAACF,UAAU;MAACiD,OAAO,EAAEpC;IAAM;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACvC;EAEA,oBACEnD,OAAA;IAAKoD,SAAS,EAAC,8BAA8B;IAAAC,QAAA,gBAC3CrD,OAAA;MAAAqD,QAAA,EAAI;IAAkB;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC3BnD,OAAA;MAAKoD,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBACpCrD,OAAA;QAAGoD,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAE3B;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJnD,OAAA;QAAKoD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,GAC/BxC,WAAW,iBACVb,OAAA;UAAMoD,SAAS,EAAC,cAAc;UAAAC,QAAA,GAAC,gBACf,EAACxC,WAAW;QAAA;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CACP,eACDnD,OAAA;UAAKoD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChCrD,OAAA;YACEoD,SAAS,EAAEnC,SAAS,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAG;YAClDqC,OAAO,EAAEA,CAAA,KAAMT,qBAAqB,CAAC,QAAQ,CAAE;YAAAQ,QAAA,EAChD;UAED;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnD,OAAA;YACEoD,SAAS,EAAEnC,SAAS,KAAK,aAAa,GAAG,QAAQ,GAAG,EAAG;YACvDqC,OAAO,EAAEA,CAAA,KAAMT,qBAAqB,CAAC,aAAa,CAAE;YAAAQ,QAAA,EACrD;UAED;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNnD,OAAA;UACEoD,SAAS,EAAE,kBAAkB3C,UAAU,GAAG,YAAY,GAAG,EAAE,EAAG;UAC9D6C,OAAO,EAAEf,iBAAkB;UAC3BgB,QAAQ,EAAE9C,UAAU,IAAIF,OAAQ;UAAA8C,QAAA,EAE/B5C,UAAU,GAAG,eAAe,GAAG;QAAc;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnD,OAAA;MAAKoD,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BrD,OAAA;QAAKoD,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BrD,OAAA;UAAAqD,QAAA,EAAI;QAAU;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnBnD,OAAA;UAAAqD,QAAA,EAAG;QAA2E;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EACjFhD,MAAM,CAACqD,MAAM,GAAG,CAAC,iBAChBxD,OAAA;UAAKoD,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACpBrD,OAAA;YAAKoD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBrD,OAAA;cAAMoD,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAa;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5CnD,OAAA;cAAMoD,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAEI,UAAU,CAACtD,MAAM,CAACA,MAAM,CAACqD,MAAM,GAAG,CAAC,CAAC,CAACE,KAAK,CAAC,CAACC,OAAO,CAAC,CAAC;YAAC;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC,eACNnD,OAAA;YAAKoD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBrD,OAAA;cAAMoD,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAY;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3CnD,OAAA;cAAMoD,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAElD,MAAM,CAACqD;YAAM;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENnD,OAAA;QAAKoD,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BrD,OAAA;UAAAqD,QAAA,EAAI;QAAW;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpBnD,OAAA;UAAAqD,QAAA,EAAG;QAAoE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,EAC1E9C,OAAO,CAACmD,MAAM,GAAG,CAAC,iBACjBxD,OAAA;UAAKoD,SAAS,EAAC,OAAO;UAAAC,QAAA,gBACpBrD,OAAA;YAAKoD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBrD,OAAA;cAAMoD,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAa;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5CnD,OAAA;cAAMoD,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAEI,UAAU,CAACpD,OAAO,CAACA,OAAO,CAACmD,MAAM,GAAG,CAAC,CAAC,CAACE,KAAK,CAAC,CAACC,OAAO,CAAC,CAAC;YAAC;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtF,CAAC,eACNnD,OAAA;YAAKoD,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBrD,OAAA;cAAMoD,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAY;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3CnD,OAAA;cAAMoD,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAEhD,OAAO,CAACmD;YAAM;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENnD,OAAA;MAAKoD,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAC7BpC,SAAS,KAAK,QAAQ,gBACrBjB,OAAA,CAACR,IAAI;QACHoE,SAAS,EAAE7C,SAAU;QACrB8C,QAAQ,EAAEpB,eAAgB;QAC1BqB,EAAE,EAAC,cAAc;QACjBV,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BrD,OAAA,CAACP,GAAG;UAACsE,QAAQ,EAAC,IAAI;UAACC,KAAK,EAAC,YAAY;UAAAX,QAAA,EAClClD,MAAM,CAACqD,MAAM,GAAG,CAAC,gBAChBxD,OAAA,CAACN,cAAc;YACbqC,IAAI,EAAE5B,MAAO;YACb8D,eAAe,EAAE9C,aAAc;YAC/B+C,gBAAgB,EAAEvB;UAA0B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,gBAEFnD,OAAA;YAAAqD,QAAA,EAAG;UAA4B;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QACnC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACNnD,OAAA,CAACP,GAAG;UAACsE,QAAQ,EAAC,KAAK;UAACC,KAAK,EAAC,aAAa;UAAAX,QAAA,EACpChD,OAAO,CAACmD,MAAM,GAAG,CAAC,gBACjBxD,OAAA,CAACL,eAAe;YACdoC,IAAI,EAAE1B,OAAQ;YACd4D,eAAe,EAAE9C,aAAc;YAC/B+C,gBAAgB,EAAEvB;UAA0B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,gBAEFnD,OAAA;YAAAqD,QAAA,EAAG;UAA6B;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QACpC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,gBAEPnD,OAAA,CAACJ,uBAAuB;QACtBuE,MAAM,EAAEpD,SAAS,KAAK,IAAI,GAAG,IAAI,GAAG,KAAM;QAC1CkD,eAAe,EAAE9C,aAAc;QAC/B+C,gBAAgB,EAAEvB;MAA0B;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C;IACF;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENnD,OAAA;MAAKoD,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BrD,OAAA;QAAAqD,QAAA,EAAI;MAAe;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxBnD,OAAA;QAAKoD,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BrD,OAAA;UAAKoD,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BrD,OAAA;YAAAqD,QAAA,EAAI;UAAmB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5BnD,OAAA;YAAAqD,QAAA,EACGlD,MAAM,CAACqD,MAAM,GAAG,CAAC,GACd,uBACErD,MAAM,CAACA,MAAM,CAACqD,MAAM,GAAG,CAAC,CAAC,CAACE,KAAK,GAAGvD,MAAM,CAACA,MAAM,CAACqD,MAAM,GAAG,CAAC,CAAC,CAACE,KAAK,GAC7D,OAAO,GACP,QAAQ,yBAEZvD,MAAM,CAACA,MAAM,CAACqD,MAAM,GAAG,CAAC,CAAC,CAACE,KAAK,GAAGvD,MAAM,CAACA,MAAM,CAACqD,MAAM,GAAG,CAAC,CAAC,CAACE,KAAK,GAC7D,UAAU,GACV,UAAU,oBACI,GACpB;UAAiC;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNnD,OAAA;UAAKoD,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BrD,OAAA;YAAAqD,QAAA,EAAI;UAAoB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7BnD,OAAA;YAAAqD,QAAA,EACGhD,OAAO,CAACmD,MAAM,GAAG,CAAC,GACf,iCACEnD,OAAO,CAACA,OAAO,CAACmD,MAAM,GAAG,CAAC,CAAC,CAACE,KAAK,GAAG,EAAE,GAClC,kDAAkD,GAClD,yDAAyD,GAC5D,GACH;UAAiC;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjD,EAAA,CArOID,iBAAiB;AAAAmE,EAAA,GAAjBnE,iBAAiB;AAuOvB,eAAeA,iBAAiB;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}