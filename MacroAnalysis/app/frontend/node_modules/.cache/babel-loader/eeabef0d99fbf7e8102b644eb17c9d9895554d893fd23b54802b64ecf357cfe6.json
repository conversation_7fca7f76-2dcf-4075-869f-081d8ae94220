{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useIsRTL } from './ThemeProvider';\nimport PopoverHeader from './PopoverHeader';\nimport PopoverBody from './PopoverBody';\nimport { getOverlayDirection } from './helpers';\nimport getInitialPopperStyles from './getInitialPopperStyles';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst Popover = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  placement = 'right',\n  className,\n  style,\n  children,\n  body,\n  arrowProps,\n  hasDoneInitialMeasure,\n  popper,\n  show,\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'popover');\n  const isRTL = useIsRTL();\n  const [primaryPlacement] = (placement == null ? void 0 : placement.split('-')) || [];\n  const bsDirection = getOverlayDirection(primaryPlacement, isRTL);\n  let computedStyle = style;\n  if (show && !hasDoneInitialMeasure) {\n    computedStyle = {\n      ...style,\n      ...getInitialPopperStyles(popper == null ? void 0 : popper.strategy)\n    };\n  }\n  return /*#__PURE__*/_jsxs(\"div\", {\n    ref: ref,\n    role: \"tooltip\",\n    style: computedStyle,\n    \"x-placement\": primaryPlacement,\n    className: classNames(className, decoratedBsPrefix, primaryPlacement && `bs-popover-${bsDirection}`),\n    ...props,\n    children: [/*#__PURE__*/_jsx(\"div\", {\n      className: \"popover-arrow\",\n      ...arrowProps\n    }), body ? /*#__PURE__*/_jsx(PopoverBody, {\n      children: children\n    }) : children]\n  });\n});\nPopover.displayName = 'Popover';\nexport default Object.assign(Popover, {\n  Header: PopoverHeader,\n  Body: PopoverBody,\n  // Default popover offset.\n  // https://github.com/twbs/bootstrap/blob/5c32767e0e0dbac2d934bcdee03719a65d3f1187/js/src/popover.js#L28\n  POPPER_OFFSET: [0, 8]\n});", "map": {"version": 3, "names": ["classNames", "React", "useBootstrapPrefix", "useIsRTL", "PopoverHeader", "PopoverBody", "getOverlayDirection", "getInitialPopperStyles", "jsx", "_jsx", "jsxs", "_jsxs", "Popover", "forwardRef", "bsPrefix", "placement", "className", "style", "children", "body", "arrowProps", "hasDoneInitialMeasure", "popper", "show", "props", "ref", "decoratedBsPrefix", "isRTL", "primaryPlacement", "split", "bsDirection", "computedStyle", "strategy", "role", "displayName", "Object", "assign", "Header", "Body", "POPPER_OFFSET"], "sources": ["/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/node_modules/react-bootstrap/esm/Popover.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix, useIsRTL } from './ThemeProvider';\nimport PopoverHeader from './PopoverHeader';\nimport PopoverBody from './PopoverBody';\nimport { getOverlayDirection } from './helpers';\nimport getInitialPopperStyles from './getInitialPopperStyles';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst Popover = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  placement = 'right',\n  className,\n  style,\n  children,\n  body,\n  arrowProps,\n  hasDoneInitialMeasure,\n  popper,\n  show,\n  ...props\n}, ref) => {\n  const decoratedBsPrefix = useBootstrapPrefix(bsPrefix, 'popover');\n  const isRTL = useIsRTL();\n  const [primaryPlacement] = (placement == null ? void 0 : placement.split('-')) || [];\n  const bsDirection = getOverlayDirection(primaryPlacement, isRTL);\n  let computedStyle = style;\n  if (show && !hasDoneInitialMeasure) {\n    computedStyle = {\n      ...style,\n      ...getInitialPopperStyles(popper == null ? void 0 : popper.strategy)\n    };\n  }\n  return /*#__PURE__*/_jsxs(\"div\", {\n    ref: ref,\n    role: \"tooltip\",\n    style: computedStyle,\n    \"x-placement\": primaryPlacement,\n    className: classNames(className, decoratedBsPrefix, primaryPlacement && `bs-popover-${bsDirection}`),\n    ...props,\n    children: [/*#__PURE__*/_jsx(\"div\", {\n      className: \"popover-arrow\",\n      ...arrowProps\n    }), body ? /*#__PURE__*/_jsx(PopoverBody, {\n      children: children\n    }) : children]\n  });\n});\nPopover.displayName = 'Popover';\nexport default Object.assign(Popover, {\n  Header: PopoverHeader,\n  Body: PopoverBody,\n  // Default popover offset.\n  // https://github.com/twbs/bootstrap/blob/5c32767e0e0dbac2d934bcdee03719a65d3f1187/js/src/popover.js#L28\n  POPPER_OFFSET: [0, 8]\n});"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,kBAAkB,EAAEC,QAAQ,QAAQ,iBAAiB;AAC9D,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,mBAAmB,QAAQ,WAAW;AAC/C,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,OAAO,GAAG,aAAaX,KAAK,CAACY,UAAU,CAAC,CAAC;EAC7CC,QAAQ;EACRC,SAAS,GAAG,OAAO;EACnBC,SAAS;EACTC,KAAK;EACLC,QAAQ;EACRC,IAAI;EACJC,UAAU;EACVC,qBAAqB;EACrBC,MAAM;EACNC,IAAI;EACJ,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAMC,iBAAiB,GAAGxB,kBAAkB,CAACY,QAAQ,EAAE,SAAS,CAAC;EACjE,MAAMa,KAAK,GAAGxB,QAAQ,CAAC,CAAC;EACxB,MAAM,CAACyB,gBAAgB,CAAC,GAAG,CAACb,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACc,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE;EACpF,MAAMC,WAAW,GAAGxB,mBAAmB,CAACsB,gBAAgB,EAAED,KAAK,CAAC;EAChE,IAAII,aAAa,GAAGd,KAAK;EACzB,IAAIM,IAAI,IAAI,CAACF,qBAAqB,EAAE;IAClCU,aAAa,GAAG;MACd,GAAGd,KAAK;MACR,GAAGV,sBAAsB,CAACe,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACU,QAAQ;IACrE,CAAC;EACH;EACA,OAAO,aAAarB,KAAK,CAAC,KAAK,EAAE;IAC/Bc,GAAG,EAAEA,GAAG;IACRQ,IAAI,EAAE,SAAS;IACfhB,KAAK,EAAEc,aAAa;IACpB,aAAa,EAAEH,gBAAgB;IAC/BZ,SAAS,EAAEhB,UAAU,CAACgB,SAAS,EAAEU,iBAAiB,EAAEE,gBAAgB,IAAI,cAAcE,WAAW,EAAE,CAAC;IACpG,GAAGN,KAAK;IACRN,QAAQ,EAAE,CAAC,aAAaT,IAAI,CAAC,KAAK,EAAE;MAClCO,SAAS,EAAE,eAAe;MAC1B,GAAGI;IACL,CAAC,CAAC,EAAED,IAAI,GAAG,aAAaV,IAAI,CAACJ,WAAW,EAAE;MACxCa,QAAQ,EAAEA;IACZ,CAAC,CAAC,GAAGA,QAAQ;EACf,CAAC,CAAC;AACJ,CAAC,CAAC;AACFN,OAAO,CAACsB,WAAW,GAAG,SAAS;AAC/B,eAAeC,MAAM,CAACC,MAAM,CAACxB,OAAO,EAAE;EACpCyB,MAAM,EAAEjC,aAAa;EACrBkC,IAAI,EAAEjC,WAAW;EACjB;EACA;EACAkC,aAAa,EAAE,CAAC,CAAC,EAAE,CAAC;AACtB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}