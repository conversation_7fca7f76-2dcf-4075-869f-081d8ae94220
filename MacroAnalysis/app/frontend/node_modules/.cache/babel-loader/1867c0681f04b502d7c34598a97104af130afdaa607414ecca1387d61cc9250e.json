{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport warning from 'warning';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport { useNavItem } from '@restart/ui/NavItem';\nimport { makeEventKey } from '@restart/ui/SelectableContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ListGroupItem = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  active,\n  disabled,\n  eventKey,\n  className,\n  variant,\n  action,\n  as,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'list-group-item');\n  const [navItemProps, meta] = useNavItem({\n    key: makeEventKey(eventKey, props.href),\n    active,\n    ...props\n  });\n  const handleClick = useEventCallback(event => {\n    if (disabled) {\n      event.preventDefault();\n      event.stopPropagation();\n      return;\n    }\n    navItemProps.onClick(event);\n  });\n  if (disabled && props.tabIndex === undefined) {\n    props.tabIndex = -1;\n    props['aria-disabled'] = true;\n  }\n  const Component = as || (action ? props.href ? 'a' : 'button' : 'div');\n  process.env.NODE_ENV !== \"production\" ? warning(as || !(!action && props.href), '`action=false` and `href` should not be used together.') : void 0;\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    ...navItemProps,\n    onClick: handleClick,\n    className: classNames(className, bsPrefix, meta.isActive && 'active', disabled && 'disabled', variant && `${bsPrefix}-${variant}`, action && `${bsPrefix}-action`)\n  });\n});\nListGroupItem.displayName = 'ListGroupItem';\nexport default ListGroupItem;", "map": {"version": 3, "names": ["classNames", "React", "warning", "useEventCallback", "useNavItem", "makeEventKey", "useBootstrapPrefix", "jsx", "_jsx", "ListGroupItem", "forwardRef", "bsPrefix", "active", "disabled", "eventKey", "className", "variant", "action", "as", "props", "ref", "navItemProps", "meta", "key", "href", "handleClick", "event", "preventDefault", "stopPropagation", "onClick", "tabIndex", "undefined", "Component", "process", "env", "NODE_ENV", "isActive", "displayName"], "sources": ["/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/node_modules/react-bootstrap/esm/ListGroupItem.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport warning from 'warning';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport { useNavItem } from '@restart/ui/NavItem';\nimport { makeEventKey } from '@restart/ui/SelectableContext';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst ListGroupItem = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  active,\n  disabled,\n  eventKey,\n  className,\n  variant,\n  action,\n  as,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'list-group-item');\n  const [navItemProps, meta] = useNavItem({\n    key: makeEventKey(eventKey, props.href),\n    active,\n    ...props\n  });\n  const handleClick = useEventCallback(event => {\n    if (disabled) {\n      event.preventDefault();\n      event.stopPropagation();\n      return;\n    }\n    navItemProps.onClick(event);\n  });\n  if (disabled && props.tabIndex === undefined) {\n    props.tabIndex = -1;\n    props['aria-disabled'] = true;\n  }\n  const Component = as || (action ? props.href ? 'a' : 'button' : 'div');\n  process.env.NODE_ENV !== \"production\" ? warning(as || !(!action && props.href), '`action=false` and `href` should not be used together.') : void 0;\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    ...navItemProps,\n    onClick: handleClick,\n    className: classNames(className, bsPrefix, meta.isActive && 'active', disabled && 'disabled', variant && `${bsPrefix}-${variant}`, action && `${bsPrefix}-action`)\n  });\n});\nListGroupItem.displayName = 'ListGroupItem';\nexport default ListGroupItem;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,SAAS;AAC7B,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,YAAY,QAAQ,+BAA+B;AAC5D,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,aAAa,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,CAAC;EACnDC,QAAQ;EACRC,MAAM;EACNC,QAAQ;EACRC,QAAQ;EACRC,SAAS;EACTC,OAAO;EACPC,MAAM;EACNC,EAAE;EACF,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTT,QAAQ,GAAGL,kBAAkB,CAACK,QAAQ,EAAE,iBAAiB,CAAC;EAC1D,MAAM,CAACU,YAAY,EAAEC,IAAI,CAAC,GAAGlB,UAAU,CAAC;IACtCmB,GAAG,EAAElB,YAAY,CAACS,QAAQ,EAAEK,KAAK,CAACK,IAAI,CAAC;IACvCZ,MAAM;IACN,GAAGO;EACL,CAAC,CAAC;EACF,MAAMM,WAAW,GAAGtB,gBAAgB,CAACuB,KAAK,IAAI;IAC5C,IAAIb,QAAQ,EAAE;MACZa,KAAK,CAACC,cAAc,CAAC,CAAC;MACtBD,KAAK,CAACE,eAAe,CAAC,CAAC;MACvB;IACF;IACAP,YAAY,CAACQ,OAAO,CAACH,KAAK,CAAC;EAC7B,CAAC,CAAC;EACF,IAAIb,QAAQ,IAAIM,KAAK,CAACW,QAAQ,KAAKC,SAAS,EAAE;IAC5CZ,KAAK,CAACW,QAAQ,GAAG,CAAC,CAAC;IACnBX,KAAK,CAAC,eAAe,CAAC,GAAG,IAAI;EAC/B;EACA,MAAMa,SAAS,GAAGd,EAAE,KAAKD,MAAM,GAAGE,KAAK,CAACK,IAAI,GAAG,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC;EACtES,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGjC,OAAO,CAACgB,EAAE,IAAI,EAAE,CAACD,MAAM,IAAIE,KAAK,CAACK,IAAI,CAAC,EAAE,wDAAwD,CAAC,GAAG,KAAK,CAAC;EAClJ,OAAO,aAAahB,IAAI,CAACwB,SAAS,EAAE;IAClCZ,GAAG,EAAEA,GAAG;IACR,GAAGD,KAAK;IACR,GAAGE,YAAY;IACfQ,OAAO,EAAEJ,WAAW;IACpBV,SAAS,EAAEf,UAAU,CAACe,SAAS,EAAEJ,QAAQ,EAAEW,IAAI,CAACc,QAAQ,IAAI,QAAQ,EAAEvB,QAAQ,IAAI,UAAU,EAAEG,OAAO,IAAI,GAAGL,QAAQ,IAAIK,OAAO,EAAE,EAAEC,MAAM,IAAI,GAAGN,QAAQ,SAAS;EACnK,CAAC,CAAC;AACJ,CAAC,CAAC;AACFF,aAAa,CAAC4B,WAAW,GAAG,eAAe;AAC3C,eAAe5B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}