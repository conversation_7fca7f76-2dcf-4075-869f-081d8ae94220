{"ast": null, "code": "import { useMemo, useRef } from 'react';\nimport useMounted from './useMounted';\nimport useWillUnmount from './useWillUnmount';\n\n/*\n * Browsers including Internet Explorer, Chrome, Safari, and Firefox store the\n * delay as a 32-bit signed integer internally. This causes an integer overflow\n * when using delays larger than 2,147,483,647 ms (about 24.8 days),\n * resulting in the timeout being executed immediately.\n *\n * via: https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/setTimeout\n */\nconst MAX_DELAY_MS = 2 ** 31 - 1;\nfunction setChainedTimeout(handleRef, fn, timeoutAtMs) {\n  const delayMs = timeoutAtMs - Date.now();\n  handleRef.current = delayMs <= MAX_DELAY_MS ? setTimeout(fn, delayMs) : setTimeout(() => setChainedTimeout(handleRef, fn, timeoutAtMs), MAX_DELAY_MS);\n}\n\n/**\n * Returns a controller object for setting a timeout that is properly cleaned up\n * once the component unmounts. New timeouts cancel and replace existing ones.\n *\n *\n *\n * ```tsx\n * const { set, clear } = useTimeout();\n * const [hello, showHello] = useState(false);\n * //Display hello after 5 seconds\n * set(() => showHello(true), 5000);\n * return (\n *   <div className=\"App\">\n *     {hello ? <h3>Hello</h3> : null}\n *   </div>\n * );\n * ```\n */\nexport default function useTimeout() {\n  const isMounted = useMounted();\n\n  // types are confused between node and web here IDK\n  const handleRef = useRef();\n  useWillUnmount(() => clearTimeout(handleRef.current));\n  return useMemo(() => {\n    const clear = () => clearTimeout(handleRef.current);\n    function set(fn, delayMs = 0) {\n      if (!isMounted()) return;\n      clear();\n      if (delayMs <= MAX_DELAY_MS) {\n        // For simplicity, if the timeout is short, just set a normal timeout.\n        handleRef.current = setTimeout(fn, delayMs);\n      } else {\n        setChainedTimeout(handleRef, fn, Date.now() + delayMs);\n      }\n    }\n    return {\n      set,\n      clear,\n      handleRef\n    };\n  }, []);\n}", "map": {"version": 3, "names": ["useMemo", "useRef", "useMounted", "useWillUnmount", "MAX_DELAY_MS", "setChainedTimeout", "handleRef", "fn", "timeoutAtMs", "delayMs", "Date", "now", "current", "setTimeout", "useTimeout", "isMounted", "clearTimeout", "clear", "set"], "sources": ["/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/node_modules/@restart/hooks/esm/useTimeout.js"], "sourcesContent": ["import { useMemo, useRef } from 'react';\nimport useMounted from './useMounted';\nimport useWillUnmount from './useWillUnmount';\n\n/*\n * Browsers including Internet Explorer, Chrome, Safari, and Firefox store the\n * delay as a 32-bit signed integer internally. This causes an integer overflow\n * when using delays larger than 2,147,483,647 ms (about 24.8 days),\n * resulting in the timeout being executed immediately.\n *\n * via: https://developer.mozilla.org/en-US/docs/Web/API/WindowOrWorkerGlobalScope/setTimeout\n */\nconst MAX_DELAY_MS = 2 ** 31 - 1;\nfunction setChainedTimeout(handleRef, fn, timeoutAtMs) {\n  const delayMs = timeoutAtMs - Date.now();\n  handleRef.current = delayMs <= MAX_DELAY_MS ? setTimeout(fn, delayMs) : setTimeout(() => setChainedTimeout(handleRef, fn, timeoutAtMs), MAX_DELAY_MS);\n}\n\n/**\n * Returns a controller object for setting a timeout that is properly cleaned up\n * once the component unmounts. New timeouts cancel and replace existing ones.\n *\n *\n *\n * ```tsx\n * const { set, clear } = useTimeout();\n * const [hello, showHello] = useState(false);\n * //Display hello after 5 seconds\n * set(() => showHello(true), 5000);\n * return (\n *   <div className=\"App\">\n *     {hello ? <h3>Hello</h3> : null}\n *   </div>\n * );\n * ```\n */\nexport default function useTimeout() {\n  const isMounted = useMounted();\n\n  // types are confused between node and web here IDK\n  const handleRef = useRef();\n  useWillUnmount(() => clearTimeout(handleRef.current));\n  return useMemo(() => {\n    const clear = () => clearTimeout(handleRef.current);\n    function set(fn, delayMs = 0) {\n      if (!isMounted()) return;\n      clear();\n      if (delayMs <= MAX_DELAY_MS) {\n        // For simplicity, if the timeout is short, just set a normal timeout.\n        handleRef.current = setTimeout(fn, delayMs);\n      } else {\n        setChainedTimeout(handleRef, fn, Date.now() + delayMs);\n      }\n    }\n    return {\n      set,\n      clear,\n      handleRef\n    };\n  }, []);\n}"], "mappings": "AAAA,SAASA,OAAO,EAAEC,MAAM,QAAQ,OAAO;AACvC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,cAAc,MAAM,kBAAkB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC;AAChC,SAASC,iBAAiBA,CAACC,SAAS,EAAEC,EAAE,EAAEC,WAAW,EAAE;EACrD,MAAMC,OAAO,GAAGD,WAAW,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC;EACxCL,SAAS,CAACM,OAAO,GAAGH,OAAO,IAAIL,YAAY,GAAGS,UAAU,CAACN,EAAE,EAAEE,OAAO,CAAC,GAAGI,UAAU,CAAC,MAAMR,iBAAiB,CAACC,SAAS,EAAEC,EAAE,EAAEC,WAAW,CAAC,EAAEJ,YAAY,CAAC;AACvJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASU,UAAUA,CAAA,EAAG;EACnC,MAAMC,SAAS,GAAGb,UAAU,CAAC,CAAC;;EAE9B;EACA,MAAMI,SAAS,GAAGL,MAAM,CAAC,CAAC;EAC1BE,cAAc,CAAC,MAAMa,YAAY,CAACV,SAAS,CAACM,OAAO,CAAC,CAAC;EACrD,OAAOZ,OAAO,CAAC,MAAM;IACnB,MAAMiB,KAAK,GAAGA,CAAA,KAAMD,YAAY,CAACV,SAAS,CAACM,OAAO,CAAC;IACnD,SAASM,GAAGA,CAACX,EAAE,EAAEE,OAAO,GAAG,CAAC,EAAE;MAC5B,IAAI,CAACM,SAAS,CAAC,CAAC,EAAE;MAClBE,KAAK,CAAC,CAAC;MACP,IAAIR,OAAO,IAAIL,YAAY,EAAE;QAC3B;QACAE,SAAS,CAACM,OAAO,GAAGC,UAAU,CAACN,EAAE,EAAEE,OAAO,CAAC;MAC7C,CAAC,MAAM;QACLJ,iBAAiB,CAACC,SAAS,EAAEC,EAAE,EAAEG,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,OAAO,CAAC;MACxD;IACF;IACA,OAAO;MACLS,GAAG;MACHD,KAAK;MACLX;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}