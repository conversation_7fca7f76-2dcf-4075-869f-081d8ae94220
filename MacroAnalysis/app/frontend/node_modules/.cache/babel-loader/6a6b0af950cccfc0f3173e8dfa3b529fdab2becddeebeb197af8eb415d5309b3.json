{"ast": null, "code": "const _excluded = [\"onKeyDown\"];\nfunction _objectWithoutPropertiesLoose(r, e) {\n  if (null == r) return {};\n  var t = {};\n  for (var n in r) if ({}.hasOwnProperty.call(r, n)) {\n    if (e.indexOf(n) >= 0) continue;\n    t[n] = r[n];\n  }\n  return t;\n}\n/* eslint-disable jsx-a11y/no-static-element-interactions */\n/* eslint-disable jsx-a11y/anchor-has-content */\n\nimport * as React from 'react';\nimport { useEventCallback } from '@restart/hooks';\nimport { useButtonProps } from './Button';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function isTrivialHref(href) {\n  return !href || href.trim() === '#';\n}\n/**\n * An generic `<a>` component that covers a few A11y cases, ensuring that\n * cases where the `href` is missing or trivial like \"#\" are treated like buttons.\n */\nconst Anchor = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n      onKeyDown\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const [buttonProps] = useButtonProps(Object.assign({\n    tagName: 'a'\n  }, props));\n  const handleKeyDown = useEventCallback(e => {\n    buttonProps.onKeyDown(e);\n    onKeyDown == null ? void 0 : onKeyDown(e);\n  });\n  if (isTrivialHref(props.href) || props.role === 'button') {\n    return /*#__PURE__*/_jsx(\"a\", Object.assign({\n      ref: ref\n    }, props, buttonProps, {\n      onKeyDown: handleKeyDown\n    }));\n  }\n  return /*#__PURE__*/_jsx(\"a\", Object.assign({\n    ref: ref\n  }, props, {\n    onKeyDown: onKeyDown\n  }));\n});\nAnchor.displayName = 'Anchor';\nexport default Anchor;", "map": {"version": 3, "names": ["_excluded", "_objectWithoutPropertiesLoose", "r", "e", "t", "n", "hasOwnProperty", "call", "indexOf", "React", "useEventCallback", "useButtonProps", "jsx", "_jsx", "isTrivialHref", "href", "trim", "<PERSON><PERSON>", "forwardRef", "_ref", "ref", "onKeyDown", "props", "buttonProps", "Object", "assign", "tagName", "handleKeyDown", "role", "displayName"], "sources": ["/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/node_modules/@restart/ui/esm/Anchor.js"], "sourcesContent": ["const _excluded = [\"onKeyDown\"];\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (e.indexOf(n) >= 0) continue; t[n] = r[n]; } return t; }\n/* eslint-disable jsx-a11y/no-static-element-interactions */\n/* eslint-disable jsx-a11y/anchor-has-content */\n\nimport * as React from 'react';\nimport { useEventCallback } from '@restart/hooks';\nimport { useButtonProps } from './Button';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport function isTrivialHref(href) {\n  return !href || href.trim() === '#';\n}\n/**\n * An generic `<a>` component that covers a few A11y cases, ensuring that\n * cases where the `href` is missing or trivial like \"#\" are treated like buttons.\n */\nconst Anchor = /*#__PURE__*/React.forwardRef((_ref, ref) => {\n  let {\n      onKeyDown\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  const [buttonProps] = useButtonProps(Object.assign({\n    tagName: 'a'\n  }, props));\n  const handleKeyDown = useEventCallback(e => {\n    buttonProps.onKeyDown(e);\n    onKeyDown == null ? void 0 : onKeyDown(e);\n  });\n  if (isTrivialHref(props.href) || props.role === 'button') {\n    return /*#__PURE__*/_jsx(\"a\", Object.assign({\n      ref: ref\n    }, props, buttonProps, {\n      onKeyDown: handleKeyDown\n    }));\n  }\n  return /*#__PURE__*/_jsx(\"a\", Object.assign({\n    ref: ref\n  }, props, {\n    onKeyDown: onKeyDown\n  }));\n});\nAnchor.displayName = 'Anchor';\nexport default Anchor;"], "mappings": "AAAA,MAAMA,SAAS,GAAG,CAAC,WAAW,CAAC;AAC/B,SAASC,6BAA6BA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,IAAI,IAAID,CAAC,EAAE,OAAO,CAAC,CAAC;EAAE,IAAIE,CAAC,GAAG,CAAC,CAAC;EAAE,KAAK,IAAIC,CAAC,IAAIH,CAAC,EAAE,IAAI,CAAC,CAAC,CAACI,cAAc,CAACC,IAAI,CAACL,CAAC,EAAEG,CAAC,CAAC,EAAE;IAAE,IAAIF,CAAC,CAACK,OAAO,CAACH,CAAC,CAAC,IAAI,CAAC,EAAE;IAAUD,CAAC,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACpM;AACA;;AAEA,OAAO,KAAKK,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,gBAAgB;AACjD,SAASC,cAAc,QAAQ,UAAU;AACzC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAE;EAClC,OAAO,CAACA,IAAI,IAAIA,IAAI,CAACC,IAAI,CAAC,CAAC,KAAK,GAAG;AACrC;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAG,aAAaR,KAAK,CAACS,UAAU,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAK;EAC1D,IAAI;MACAC;IACF,CAAC,GAAGF,IAAI;IACRG,KAAK,GAAGrB,6BAA6B,CAACkB,IAAI,EAAEnB,SAAS,CAAC;EACxD,MAAM,CAACuB,WAAW,CAAC,GAAGZ,cAAc,CAACa,MAAM,CAACC,MAAM,CAAC;IACjDC,OAAO,EAAE;EACX,CAAC,EAAEJ,KAAK,CAAC,CAAC;EACV,MAAMK,aAAa,GAAGjB,gBAAgB,CAACP,CAAC,IAAI;IAC1CoB,WAAW,CAACF,SAAS,CAAClB,CAAC,CAAC;IACxBkB,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAClB,CAAC,CAAC;EAC3C,CAAC,CAAC;EACF,IAAIW,aAAa,CAACQ,KAAK,CAACP,IAAI,CAAC,IAAIO,KAAK,CAACM,IAAI,KAAK,QAAQ,EAAE;IACxD,OAAO,aAAaf,IAAI,CAAC,GAAG,EAAEW,MAAM,CAACC,MAAM,CAAC;MAC1CL,GAAG,EAAEA;IACP,CAAC,EAAEE,KAAK,EAAEC,WAAW,EAAE;MACrBF,SAAS,EAAEM;IACb,CAAC,CAAC,CAAC;EACL;EACA,OAAO,aAAad,IAAI,CAAC,GAAG,EAAEW,MAAM,CAACC,MAAM,CAAC;IAC1CL,GAAG,EAAEA;EACP,CAAC,EAAEE,KAAK,EAAE;IACRD,SAAS,EAAEA;EACb,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFJ,MAAM,CAACY,WAAW,GAAG,QAAQ;AAC7B,eAAeZ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}