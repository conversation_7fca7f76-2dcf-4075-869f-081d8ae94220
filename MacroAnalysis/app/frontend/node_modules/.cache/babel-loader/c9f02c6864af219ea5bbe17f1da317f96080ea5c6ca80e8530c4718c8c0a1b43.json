{"ast": null, "code": "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport SelectableContext from '@restart/ui/SelectableContext';\nimport TabContext from '@restart/ui/TabContext';\nimport { useTabPanel } from '@restart/ui/TabPanel';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport Fade from './Fade';\nimport getTabTransitionComponent from './getTabTransitionComponent';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst TabPane = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  transition,\n  ...props\n}, ref) => {\n  const [{\n    className,\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'div',\n    ...rest\n  }, {\n    isActive,\n    onEnter,\n    onEntering,\n    onEntered,\n    onExit,\n    onExiting,\n    onExited,\n    mountOnEnter,\n    unmountOnExit,\n    transition: Transition = Fade\n  }] = useTabPanel({\n    ...props,\n    transition: getTabTransitionComponent(transition)\n  });\n  const prefix = useBootstrapPrefix(bsPrefix, 'tab-pane');\n\n  // We provide an empty the TabContext so `<Nav>`s in `<TabPanel>`s don't\n  // conflict with the top level one.\n  return /*#__PURE__*/_jsx(TabContext.Provider, {\n    value: null,\n    children: /*#__PURE__*/_jsx(SelectableContext.Provider, {\n      value: null,\n      children: /*#__PURE__*/_jsx(Transition, {\n        in: isActive,\n        onEnter: onEnter,\n        onEntering: onEntering,\n        onEntered: onEntered,\n        onExit: onExit,\n        onExiting: onExiting,\n        onExited: onExited,\n        mountOnEnter: mountOnEnter,\n        unmountOnExit: unmountOnExit,\n        children: /*#__PURE__*/_jsx(Component, {\n          ...rest,\n          ref: ref,\n          className: classNames(className, prefix, isActive && 'active')\n        })\n      })\n    })\n  });\n});\nTabPane.displayName = 'TabPane';\nexport default TabPane;", "map": {"version": 3, "names": ["classNames", "React", "SelectableContext", "TabContext", "useTabPanel", "useBootstrapPrefix", "Fade", "getTabTransitionComponent", "jsx", "_jsx", "TabPane", "forwardRef", "bsPrefix", "transition", "props", "ref", "className", "as", "Component", "rest", "isActive", "onEnter", "onEntering", "onEntered", "onExit", "onExiting", "onExited", "mountOnEnter", "unmountOnExit", "Transition", "prefix", "Provider", "value", "children", "in", "displayName"], "sources": ["/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/node_modules/react-bootstrap/esm/TabPane.js"], "sourcesContent": ["\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport SelectableContext from '@restart/ui/SelectableContext';\nimport TabContext from '@restart/ui/TabContext';\nimport { useTabPanel } from '@restart/ui/TabPanel';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport Fade from './Fade';\nimport getTabTransitionComponent from './getTabTransitionComponent';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst TabPane = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  transition,\n  ...props\n}, ref) => {\n  const [{\n    className,\n    // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n    as: Component = 'div',\n    ...rest\n  }, {\n    isActive,\n    onEnter,\n    onEntering,\n    onEntered,\n    onExit,\n    onExiting,\n    onExited,\n    mountOnEnter,\n    unmountOnExit,\n    transition: Transition = Fade\n  }] = useTabPanel({\n    ...props,\n    transition: getTabTransitionComponent(transition)\n  });\n  const prefix = useBootstrapPrefix(bsPrefix, 'tab-pane');\n\n  // We provide an empty the TabContext so `<Nav>`s in `<TabPanel>`s don't\n  // conflict with the top level one.\n  return /*#__PURE__*/_jsx(TabContext.Provider, {\n    value: null,\n    children: /*#__PURE__*/_jsx(SelectableContext.Provider, {\n      value: null,\n      children: /*#__PURE__*/_jsx(Transition, {\n        in: isActive,\n        onEnter: onEnter,\n        onEntering: onEntering,\n        onEntered: onEntered,\n        onExit: onExit,\n        onExiting: onExiting,\n        onExited: onExited,\n        mountOnEnter: mountOnEnter,\n        unmountOnExit: unmountOnExit,\n        children: /*#__PURE__*/_jsx(Component, {\n          ...rest,\n          ref: ref,\n          className: classNames(className, prefix, isActive && 'active')\n        })\n      })\n    })\n  });\n});\nTabPane.displayName = 'TabPane';\nexport default TabPane;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,SAASC,WAAW,QAAQ,sBAAsB;AAClD,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,yBAAyB,MAAM,6BAA6B;AACnE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,OAAO,GAAG,aAAaT,KAAK,CAACU,UAAU,CAAC,CAAC;EAC7CC,QAAQ;EACRC,UAAU;EACV,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACT,MAAM,CAAC;IACLC,SAAS;IACT;IACAC,EAAE,EAAEC,SAAS,GAAG,KAAK;IACrB,GAAGC;EACL,CAAC,EAAE;IACDC,QAAQ;IACRC,OAAO;IACPC,UAAU;IACVC,SAAS;IACTC,MAAM;IACNC,SAAS;IACTC,QAAQ;IACRC,YAAY;IACZC,aAAa;IACbf,UAAU,EAAEgB,UAAU,GAAGvB;EAC3B,CAAC,CAAC,GAAGF,WAAW,CAAC;IACf,GAAGU,KAAK;IACRD,UAAU,EAAEN,yBAAyB,CAACM,UAAU;EAClD,CAAC,CAAC;EACF,MAAMiB,MAAM,GAAGzB,kBAAkB,CAACO,QAAQ,EAAE,UAAU,CAAC;;EAEvD;EACA;EACA,OAAO,aAAaH,IAAI,CAACN,UAAU,CAAC4B,QAAQ,EAAE;IAC5CC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAE,aAAaxB,IAAI,CAACP,iBAAiB,CAAC6B,QAAQ,EAAE;MACtDC,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE,aAAaxB,IAAI,CAACoB,UAAU,EAAE;QACtCK,EAAE,EAAEd,QAAQ;QACZC,OAAO,EAAEA,OAAO;QAChBC,UAAU,EAAEA,UAAU;QACtBC,SAAS,EAAEA,SAAS;QACpBC,MAAM,EAAEA,MAAM;QACdC,SAAS,EAAEA,SAAS;QACpBC,QAAQ,EAAEA,QAAQ;QAClBC,YAAY,EAAEA,YAAY;QAC1BC,aAAa,EAAEA,aAAa;QAC5BK,QAAQ,EAAE,aAAaxB,IAAI,CAACS,SAAS,EAAE;UACrC,GAAGC,IAAI;UACPJ,GAAG,EAAEA,GAAG;UACRC,SAAS,EAAEhB,UAAU,CAACgB,SAAS,EAAEc,MAAM,EAAEV,QAAQ,IAAI,QAAQ;QAC/D,CAAC;MACH,CAAC;IACH,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFV,OAAO,CAACyB,WAAW,GAAG,SAAS;AAC/B,eAAezB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}