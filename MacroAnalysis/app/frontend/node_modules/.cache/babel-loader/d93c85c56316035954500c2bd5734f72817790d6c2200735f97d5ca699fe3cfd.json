{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DropdownHeader = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  role = 'heading',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'dropdown-header');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    role: role,\n    ...props\n  });\n});\nDropdownHeader.displayName = 'DropdownHeader';\nexport default DropdownHeader;", "map": {"version": 3, "names": ["React", "classNames", "useBootstrapPrefix", "jsx", "_jsx", "DropdownHeader", "forwardRef", "className", "bsPrefix", "as", "Component", "role", "props", "ref", "displayName"], "sources": ["/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/node_modules/react-bootstrap/esm/DropdownHeader.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DropdownHeader = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  role = 'heading',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'dropdown-header');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    role: role,\n    ...props\n  });\n});\nDropdownHeader.displayName = 'DropdownHeader';\nexport default DropdownHeader;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,cAAc,GAAG,aAAaL,KAAK,CAACM,UAAU,CAAC,CAAC;EACpDC,SAAS;EACTC,QAAQ;EACRC,EAAE,EAAEC,SAAS,GAAG,KAAK;EACrBC,IAAI,GAAG,SAAS;EAChB,GAAGC;AACL,CAAC,EAAEC,GAAG,KAAK;EACTL,QAAQ,GAAGN,kBAAkB,CAACM,QAAQ,EAAE,iBAAiB,CAAC;EAC1D,OAAO,aAAaJ,IAAI,CAACM,SAAS,EAAE;IAClCG,GAAG,EAAEA,GAAG;IACRN,SAAS,EAAEN,UAAU,CAACM,SAAS,EAAEC,QAAQ,CAAC;IAC1CG,IAAI,EAAEA,IAAI;IACV,GAAGC;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFP,cAAc,CAACS,WAAW,GAAG,gBAAgB;AAC7C,eAAeT,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}