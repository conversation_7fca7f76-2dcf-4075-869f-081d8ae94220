{"ast": null, "code": "import qsa from 'dom-helpers/querySelectorAll';\nimport addEventListener from 'dom-helpers/addEventListener';\nimport { useCallback, useRef, useEffect, useMemo, useContext } from 'react';\nimport * as React from 'react';\nimport { useUncontrolledProp } from 'uncontrollable';\nimport usePrevious from '@restart/hooks/usePrevious';\nimport useForceUpdate from '@restart/hooks/useForceUpdate';\nimport useEventListener from '@restart/hooks/useEventListener';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport DropdownContext from './DropdownContext';\nimport DropdownMenu from './DropdownMenu';\nimport DropdownToggle, { isRoleMenu } from './DropdownToggle';\nimport DropdownItem from './DropdownItem';\nimport SelectableContext from './SelectableContext';\nimport { dataAttr } from './DataKey';\nimport useWindow from './useWindow';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction useRefWithUpdate() {\n  const forceUpdate = useForceUpdate();\n  const ref = useRef(null);\n  const attachRef = useCallback(element => {\n    ref.current = element;\n    // ensure that a menu set triggers an update for consumers\n    forceUpdate();\n  }, [forceUpdate]);\n  return [ref, attachRef];\n}\n\n/**\n * @displayName Dropdown\n * @public\n */\nfunction Dropdown({\n  defaultShow,\n  show: rawShow,\n  onSelect,\n  onToggle: rawOnToggle,\n  itemSelector = `* [${dataAttr('dropdown-item')}]`,\n  focusFirstItemOnShow,\n  placement = 'bottom-start',\n  children\n}) {\n  const window = useWindow();\n  const [show, onToggle] = useUncontrolledProp(rawShow, defaultShow, rawOnToggle);\n\n  // We use normal refs instead of useCallbackRef in order to populate the\n  // the value as quickly as possible, otherwise the effect to focus the element\n  // may run before the state value is set\n  const [menuRef, setMenu] = useRefWithUpdate();\n  const menuElement = menuRef.current;\n  const [toggleRef, setToggle] = useRefWithUpdate();\n  const toggleElement = toggleRef.current;\n  const lastShow = usePrevious(show);\n  const lastSourceEvent = useRef(null);\n  const focusInDropdown = useRef(false);\n  const onSelectCtx = useContext(SelectableContext);\n  const toggle = useCallback((nextShow, event, source = event == null ? void 0 : event.type) => {\n    onToggle(nextShow, {\n      originalEvent: event,\n      source\n    });\n  }, [onToggle]);\n  const handleSelect = useEventCallback((key, event) => {\n    onSelect == null ? void 0 : onSelect(key, event);\n    toggle(false, event, 'select');\n    if (!event.isPropagationStopped()) {\n      onSelectCtx == null ? void 0 : onSelectCtx(key, event);\n    }\n  });\n  const context = useMemo(() => ({\n    toggle,\n    placement,\n    show,\n    menuElement,\n    toggleElement,\n    setMenu,\n    setToggle\n  }), [toggle, placement, show, menuElement, toggleElement, setMenu, setToggle]);\n  if (menuElement && lastShow && !show) {\n    focusInDropdown.current = menuElement.contains(menuElement.ownerDocument.activeElement);\n  }\n  const focusToggle = useEventCallback(() => {\n    if (toggleElement && toggleElement.focus) {\n      toggleElement.focus();\n    }\n  });\n  const maybeFocusFirst = useEventCallback(() => {\n    const type = lastSourceEvent.current;\n    let focusType = focusFirstItemOnShow;\n    if (focusType == null) {\n      focusType = menuRef.current && isRoleMenu(menuRef.current) ? 'keyboard' : false;\n    }\n    if (focusType === false || focusType === 'keyboard' && !/^key.+$/.test(type)) {\n      return;\n    }\n    const first = qsa(menuRef.current, itemSelector)[0];\n    if (first && first.focus) first.focus();\n  });\n  useEffect(() => {\n    if (show) maybeFocusFirst();else if (focusInDropdown.current) {\n      focusInDropdown.current = false;\n      focusToggle();\n    }\n    // only `show` should be changing\n  }, [show, focusInDropdown, focusToggle, maybeFocusFirst]);\n  useEffect(() => {\n    lastSourceEvent.current = null;\n  });\n  const getNextFocusedChild = (current, offset) => {\n    if (!menuRef.current) return null;\n    const items = qsa(menuRef.current, itemSelector);\n    let index = items.indexOf(current) + offset;\n    index = Math.max(0, Math.min(index, items.length));\n    return items[index];\n  };\n  useEventListener(useCallback(() => window.document, [window]), 'keydown', event => {\n    var _menuRef$current, _toggleRef$current;\n    const {\n      key\n    } = event;\n    const target = event.target;\n    const fromMenu = (_menuRef$current = menuRef.current) == null ? void 0 : _menuRef$current.contains(target);\n    const fromToggle = (_toggleRef$current = toggleRef.current) == null ? void 0 : _toggleRef$current.contains(target);\n\n    // Second only to https://github.com/twbs/bootstrap/blob/8cfbf6933b8a0146ac3fbc369f19e520bd1ebdac/js/src/dropdown.js#L400\n    // in inscrutability\n    const isInput = /input|textarea/i.test(target.tagName);\n    if (isInput && (key === ' ' || key !== 'Escape' && fromMenu || key === 'Escape' && target.type === 'search')) {\n      return;\n    }\n    if (!fromMenu && !fromToggle) {\n      return;\n    }\n    if (key === 'Tab' && (!menuRef.current || !show)) {\n      return;\n    }\n    lastSourceEvent.current = event.type;\n    const meta = {\n      originalEvent: event,\n      source: event.type\n    };\n    switch (key) {\n      case 'ArrowUp':\n        {\n          const next = getNextFocusedChild(target, -1);\n          if (next && next.focus) next.focus();\n          event.preventDefault();\n          return;\n        }\n      case 'ArrowDown':\n        event.preventDefault();\n        if (!show) {\n          onToggle(true, meta);\n        } else {\n          const next = getNextFocusedChild(target, 1);\n          if (next && next.focus) next.focus();\n        }\n        return;\n      case 'Tab':\n        // on keydown the target is the element being tabbed FROM, we need that\n        // to know if this event is relevant to this dropdown (e.g. in this menu).\n        // On `keyup` the target is the element being tagged TO which we use to check\n        // if focus has left the menu\n        addEventListener(target.ownerDocument, 'keyup', e => {\n          var _menuRef$current2;\n          if (e.key === 'Tab' && !e.target || !((_menuRef$current2 = menuRef.current) != null && _menuRef$current2.contains(e.target))) {\n            onToggle(false, meta);\n          }\n        }, {\n          once: true\n        });\n        break;\n      case 'Escape':\n        if (key === 'Escape') {\n          event.preventDefault();\n          event.stopPropagation();\n        }\n        onToggle(false, meta);\n        break;\n      default:\n    }\n  });\n  return /*#__PURE__*/_jsx(SelectableContext.Provider, {\n    value: handleSelect,\n    children: /*#__PURE__*/_jsx(DropdownContext.Provider, {\n      value: context,\n      children: children\n    })\n  });\n}\nDropdown.displayName = 'Dropdown';\nDropdown.Menu = DropdownMenu;\nDropdown.Toggle = DropdownToggle;\nDropdown.Item = DropdownItem;\nexport default Dropdown;", "map": {"version": 3, "names": ["qsa", "addEventListener", "useCallback", "useRef", "useEffect", "useMemo", "useContext", "React", "useUncontrolledProp", "usePrevious", "useForceUpdate", "useEventListener", "useEventCallback", "DropdownContext", "DropdownMenu", "DropdownToggle", "isRoleMenu", "DropdownItem", "SelectableContext", "dataAttr", "useWindow", "jsx", "_jsx", "useRefWithUpdate", "forceUpdate", "ref", "attachRef", "element", "current", "Dropdown", "defaultShow", "show", "rawShow", "onSelect", "onToggle", "rawOnToggle", "itemSelector", "focusFirstItemOnShow", "placement", "children", "window", "menuRef", "setMenu", "menuElement", "toggleRef", "<PERSON><PERSON><PERSON><PERSON>", "toggleElement", "lastShow", "lastSourceEvent", "focusInDropdown", "onSelectCtx", "toggle", "nextShow", "event", "source", "type", "originalEvent", "handleSelect", "key", "isPropagationStopped", "context", "contains", "ownerDocument", "activeElement", "focusToggle", "focus", "maybeFocus<PERSON><PERSON><PERSON>", "focusType", "test", "first", "getNextFocusedChild", "offset", "items", "index", "indexOf", "Math", "max", "min", "length", "document", "_menuRef$current", "_toggleRef$current", "target", "fromMenu", "fromToggle", "isInput", "tagName", "meta", "next", "preventDefault", "e", "_menuRef$current2", "once", "stopPropagation", "Provider", "value", "displayName", "<PERSON><PERSON>", "Toggle", "<PERSON><PERSON>"], "sources": ["/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/node_modules/@restart/ui/esm/Dropdown.js"], "sourcesContent": ["import qsa from 'dom-helpers/querySelectorAll';\nimport addEventListener from 'dom-helpers/addEventListener';\nimport { useCallback, useRef, useEffect, useMemo, useContext } from 'react';\nimport * as React from 'react';\nimport { useUncontrolledProp } from 'uncontrollable';\nimport usePrevious from '@restart/hooks/usePrevious';\nimport useForceUpdate from '@restart/hooks/useForceUpdate';\nimport useEventListener from '@restart/hooks/useEventListener';\nimport useEventCallback from '@restart/hooks/useEventCallback';\nimport DropdownContext from './DropdownContext';\nimport DropdownMenu from './DropdownMenu';\nimport DropdownToggle, { isRoleMenu } from './DropdownToggle';\nimport DropdownItem from './DropdownItem';\nimport SelectableContext from './SelectableContext';\nimport { dataAttr } from './DataKey';\nimport useWindow from './useWindow';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction useRefWithUpdate() {\n  const forceUpdate = useForceUpdate();\n  const ref = useRef(null);\n  const attachRef = useCallback(element => {\n    ref.current = element;\n    // ensure that a menu set triggers an update for consumers\n    forceUpdate();\n  }, [forceUpdate]);\n  return [ref, attachRef];\n}\n\n/**\n * @displayName Dropdown\n * @public\n */\nfunction Dropdown({\n  defaultShow,\n  show: rawShow,\n  onSelect,\n  onToggle: rawOnToggle,\n  itemSelector = `* [${dataAttr('dropdown-item')}]`,\n  focusFirstItemOnShow,\n  placement = 'bottom-start',\n  children\n}) {\n  const window = useWindow();\n  const [show, onToggle] = useUncontrolledProp(rawShow, defaultShow, rawOnToggle);\n\n  // We use normal refs instead of useCallbackRef in order to populate the\n  // the value as quickly as possible, otherwise the effect to focus the element\n  // may run before the state value is set\n  const [menuRef, setMenu] = useRefWithUpdate();\n  const menuElement = menuRef.current;\n  const [toggleRef, setToggle] = useRefWithUpdate();\n  const toggleElement = toggleRef.current;\n  const lastShow = usePrevious(show);\n  const lastSourceEvent = useRef(null);\n  const focusInDropdown = useRef(false);\n  const onSelectCtx = useContext(SelectableContext);\n  const toggle = useCallback((nextShow, event, source = event == null ? void 0 : event.type) => {\n    onToggle(nextShow, {\n      originalEvent: event,\n      source\n    });\n  }, [onToggle]);\n  const handleSelect = useEventCallback((key, event) => {\n    onSelect == null ? void 0 : onSelect(key, event);\n    toggle(false, event, 'select');\n    if (!event.isPropagationStopped()) {\n      onSelectCtx == null ? void 0 : onSelectCtx(key, event);\n    }\n  });\n  const context = useMemo(() => ({\n    toggle,\n    placement,\n    show,\n    menuElement,\n    toggleElement,\n    setMenu,\n    setToggle\n  }), [toggle, placement, show, menuElement, toggleElement, setMenu, setToggle]);\n  if (menuElement && lastShow && !show) {\n    focusInDropdown.current = menuElement.contains(menuElement.ownerDocument.activeElement);\n  }\n  const focusToggle = useEventCallback(() => {\n    if (toggleElement && toggleElement.focus) {\n      toggleElement.focus();\n    }\n  });\n  const maybeFocusFirst = useEventCallback(() => {\n    const type = lastSourceEvent.current;\n    let focusType = focusFirstItemOnShow;\n    if (focusType == null) {\n      focusType = menuRef.current && isRoleMenu(menuRef.current) ? 'keyboard' : false;\n    }\n    if (focusType === false || focusType === 'keyboard' && !/^key.+$/.test(type)) {\n      return;\n    }\n    const first = qsa(menuRef.current, itemSelector)[0];\n    if (first && first.focus) first.focus();\n  });\n  useEffect(() => {\n    if (show) maybeFocusFirst();else if (focusInDropdown.current) {\n      focusInDropdown.current = false;\n      focusToggle();\n    }\n    // only `show` should be changing\n  }, [show, focusInDropdown, focusToggle, maybeFocusFirst]);\n  useEffect(() => {\n    lastSourceEvent.current = null;\n  });\n  const getNextFocusedChild = (current, offset) => {\n    if (!menuRef.current) return null;\n    const items = qsa(menuRef.current, itemSelector);\n    let index = items.indexOf(current) + offset;\n    index = Math.max(0, Math.min(index, items.length));\n    return items[index];\n  };\n  useEventListener(useCallback(() => window.document, [window]), 'keydown', event => {\n    var _menuRef$current, _toggleRef$current;\n    const {\n      key\n    } = event;\n    const target = event.target;\n    const fromMenu = (_menuRef$current = menuRef.current) == null ? void 0 : _menuRef$current.contains(target);\n    const fromToggle = (_toggleRef$current = toggleRef.current) == null ? void 0 : _toggleRef$current.contains(target);\n\n    // Second only to https://github.com/twbs/bootstrap/blob/8cfbf6933b8a0146ac3fbc369f19e520bd1ebdac/js/src/dropdown.js#L400\n    // in inscrutability\n    const isInput = /input|textarea/i.test(target.tagName);\n    if (isInput && (key === ' ' || key !== 'Escape' && fromMenu || key === 'Escape' && target.type === 'search')) {\n      return;\n    }\n    if (!fromMenu && !fromToggle) {\n      return;\n    }\n    if (key === 'Tab' && (!menuRef.current || !show)) {\n      return;\n    }\n    lastSourceEvent.current = event.type;\n    const meta = {\n      originalEvent: event,\n      source: event.type\n    };\n    switch (key) {\n      case 'ArrowUp':\n        {\n          const next = getNextFocusedChild(target, -1);\n          if (next && next.focus) next.focus();\n          event.preventDefault();\n          return;\n        }\n      case 'ArrowDown':\n        event.preventDefault();\n        if (!show) {\n          onToggle(true, meta);\n        } else {\n          const next = getNextFocusedChild(target, 1);\n          if (next && next.focus) next.focus();\n        }\n        return;\n      case 'Tab':\n        // on keydown the target is the element being tabbed FROM, we need that\n        // to know if this event is relevant to this dropdown (e.g. in this menu).\n        // On `keyup` the target is the element being tagged TO which we use to check\n        // if focus has left the menu\n        addEventListener(target.ownerDocument, 'keyup', e => {\n          var _menuRef$current2;\n          if (e.key === 'Tab' && !e.target || !((_menuRef$current2 = menuRef.current) != null && _menuRef$current2.contains(e.target))) {\n            onToggle(false, meta);\n          }\n        }, {\n          once: true\n        });\n        break;\n      case 'Escape':\n        if (key === 'Escape') {\n          event.preventDefault();\n          event.stopPropagation();\n        }\n        onToggle(false, meta);\n        break;\n      default:\n    }\n  });\n  return /*#__PURE__*/_jsx(SelectableContext.Provider, {\n    value: handleSelect,\n    children: /*#__PURE__*/_jsx(DropdownContext.Provider, {\n      value: context,\n      children: children\n    })\n  });\n}\nDropdown.displayName = 'Dropdown';\nDropdown.Menu = DropdownMenu;\nDropdown.Toggle = DropdownToggle;\nDropdown.Item = DropdownItem;\nexport default Dropdown;"], "mappings": "AAAA,OAAOA,GAAG,MAAM,8BAA8B;AAC9C,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,SAASC,WAAW,EAAEC,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAEC,UAAU,QAAQ,OAAO;AAC3E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,OAAOC,WAAW,MAAM,4BAA4B;AACpD,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,cAAc,IAAIC,UAAU,QAAQ,kBAAkB;AAC7D,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,QAAQ,QAAQ,WAAW;AACpC,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,gBAAgBA,CAAA,EAAG;EAC1B,MAAMC,WAAW,GAAGd,cAAc,CAAC,CAAC;EACpC,MAAMe,GAAG,GAAGtB,MAAM,CAAC,IAAI,CAAC;EACxB,MAAMuB,SAAS,GAAGxB,WAAW,CAACyB,OAAO,IAAI;IACvCF,GAAG,CAACG,OAAO,GAAGD,OAAO;IACrB;IACAH,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EACjB,OAAO,CAACC,GAAG,EAAEC,SAAS,CAAC;AACzB;;AAEA;AACA;AACA;AACA;AACA,SAASG,QAAQA,CAAC;EAChBC,WAAW;EACXC,IAAI,EAAEC,OAAO;EACbC,QAAQ;EACRC,QAAQ,EAAEC,WAAW;EACrBC,YAAY,GAAG,MAAMjB,QAAQ,CAAC,eAAe,CAAC,GAAG;EACjDkB,oBAAoB;EACpBC,SAAS,GAAG,cAAc;EAC1BC;AACF,CAAC,EAAE;EACD,MAAMC,MAAM,GAAGpB,SAAS,CAAC,CAAC;EAC1B,MAAM,CAACW,IAAI,EAAEG,QAAQ,CAAC,GAAG1B,mBAAmB,CAACwB,OAAO,EAAEF,WAAW,EAAEK,WAAW,CAAC;;EAE/E;EACA;EACA;EACA,MAAM,CAACM,OAAO,EAAEC,OAAO,CAAC,GAAGnB,gBAAgB,CAAC,CAAC;EAC7C,MAAMoB,WAAW,GAAGF,OAAO,CAACb,OAAO;EACnC,MAAM,CAACgB,SAAS,EAAEC,SAAS,CAAC,GAAGtB,gBAAgB,CAAC,CAAC;EACjD,MAAMuB,aAAa,GAAGF,SAAS,CAAChB,OAAO;EACvC,MAAMmB,QAAQ,GAAGtC,WAAW,CAACsB,IAAI,CAAC;EAClC,MAAMiB,eAAe,GAAG7C,MAAM,CAAC,IAAI,CAAC;EACpC,MAAM8C,eAAe,GAAG9C,MAAM,CAAC,KAAK,CAAC;EACrC,MAAM+C,WAAW,GAAG5C,UAAU,CAACY,iBAAiB,CAAC;EACjD,MAAMiC,MAAM,GAAGjD,WAAW,CAAC,CAACkD,QAAQ,EAAEC,KAAK,EAAEC,MAAM,GAAGD,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACE,IAAI,KAAK;IAC5FrB,QAAQ,CAACkB,QAAQ,EAAE;MACjBI,aAAa,EAAEH,KAAK;MACpBC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACpB,QAAQ,CAAC,CAAC;EACd,MAAMuB,YAAY,GAAG7C,gBAAgB,CAAC,CAAC8C,GAAG,EAAEL,KAAK,KAAK;IACpDpB,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACyB,GAAG,EAAEL,KAAK,CAAC;IAChDF,MAAM,CAAC,KAAK,EAAEE,KAAK,EAAE,QAAQ,CAAC;IAC9B,IAAI,CAACA,KAAK,CAACM,oBAAoB,CAAC,CAAC,EAAE;MACjCT,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACQ,GAAG,EAAEL,KAAK,CAAC;IACxD;EACF,CAAC,CAAC;EACF,MAAMO,OAAO,GAAGvD,OAAO,CAAC,OAAO;IAC7B8C,MAAM;IACNb,SAAS;IACTP,IAAI;IACJY,WAAW;IACXG,aAAa;IACbJ,OAAO;IACPG;EACF,CAAC,CAAC,EAAE,CAACM,MAAM,EAAEb,SAAS,EAAEP,IAAI,EAAEY,WAAW,EAAEG,aAAa,EAAEJ,OAAO,EAAEG,SAAS,CAAC,CAAC;EAC9E,IAAIF,WAAW,IAAII,QAAQ,IAAI,CAAChB,IAAI,EAAE;IACpCkB,eAAe,CAACrB,OAAO,GAAGe,WAAW,CAACkB,QAAQ,CAAClB,WAAW,CAACmB,aAAa,CAACC,aAAa,CAAC;EACzF;EACA,MAAMC,WAAW,GAAGpD,gBAAgB,CAAC,MAAM;IACzC,IAAIkC,aAAa,IAAIA,aAAa,CAACmB,KAAK,EAAE;MACxCnB,aAAa,CAACmB,KAAK,CAAC,CAAC;IACvB;EACF,CAAC,CAAC;EACF,MAAMC,eAAe,GAAGtD,gBAAgB,CAAC,MAAM;IAC7C,MAAM2C,IAAI,GAAGP,eAAe,CAACpB,OAAO;IACpC,IAAIuC,SAAS,GAAG9B,oBAAoB;IACpC,IAAI8B,SAAS,IAAI,IAAI,EAAE;MACrBA,SAAS,GAAG1B,OAAO,CAACb,OAAO,IAAIZ,UAAU,CAACyB,OAAO,CAACb,OAAO,CAAC,GAAG,UAAU,GAAG,KAAK;IACjF;IACA,IAAIuC,SAAS,KAAK,KAAK,IAAIA,SAAS,KAAK,UAAU,IAAI,CAAC,SAAS,CAACC,IAAI,CAACb,IAAI,CAAC,EAAE;MAC5E;IACF;IACA,MAAMc,KAAK,GAAGrE,GAAG,CAACyC,OAAO,CAACb,OAAO,EAAEQ,YAAY,CAAC,CAAC,CAAC,CAAC;IACnD,IAAIiC,KAAK,IAAIA,KAAK,CAACJ,KAAK,EAAEI,KAAK,CAACJ,KAAK,CAAC,CAAC;EACzC,CAAC,CAAC;EACF7D,SAAS,CAAC,MAAM;IACd,IAAI2B,IAAI,EAAEmC,eAAe,CAAC,CAAC,CAAC,KAAK,IAAIjB,eAAe,CAACrB,OAAO,EAAE;MAC5DqB,eAAe,CAACrB,OAAO,GAAG,KAAK;MAC/BoC,WAAW,CAAC,CAAC;IACf;IACA;EACF,CAAC,EAAE,CAACjC,IAAI,EAAEkB,eAAe,EAAEe,WAAW,EAAEE,eAAe,CAAC,CAAC;EACzD9D,SAAS,CAAC,MAAM;IACd4C,eAAe,CAACpB,OAAO,GAAG,IAAI;EAChC,CAAC,CAAC;EACF,MAAM0C,mBAAmB,GAAGA,CAAC1C,OAAO,EAAE2C,MAAM,KAAK;IAC/C,IAAI,CAAC9B,OAAO,CAACb,OAAO,EAAE,OAAO,IAAI;IACjC,MAAM4C,KAAK,GAAGxE,GAAG,CAACyC,OAAO,CAACb,OAAO,EAAEQ,YAAY,CAAC;IAChD,IAAIqC,KAAK,GAAGD,KAAK,CAACE,OAAO,CAAC9C,OAAO,CAAC,GAAG2C,MAAM;IAC3CE,KAAK,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACJ,KAAK,EAAED,KAAK,CAACM,MAAM,CAAC,CAAC;IAClD,OAAON,KAAK,CAACC,KAAK,CAAC;EACrB,CAAC;EACD9D,gBAAgB,CAACT,WAAW,CAAC,MAAMsC,MAAM,CAACuC,QAAQ,EAAE,CAACvC,MAAM,CAAC,CAAC,EAAE,SAAS,EAAEa,KAAK,IAAI;IACjF,IAAI2B,gBAAgB,EAAEC,kBAAkB;IACxC,MAAM;MACJvB;IACF,CAAC,GAAGL,KAAK;IACT,MAAM6B,MAAM,GAAG7B,KAAK,CAAC6B,MAAM;IAC3B,MAAMC,QAAQ,GAAG,CAACH,gBAAgB,GAAGvC,OAAO,CAACb,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoD,gBAAgB,CAACnB,QAAQ,CAACqB,MAAM,CAAC;IAC1G,MAAME,UAAU,GAAG,CAACH,kBAAkB,GAAGrC,SAAS,CAAChB,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGqD,kBAAkB,CAACpB,QAAQ,CAACqB,MAAM,CAAC;;IAElH;IACA;IACA,MAAMG,OAAO,GAAG,iBAAiB,CAACjB,IAAI,CAACc,MAAM,CAACI,OAAO,CAAC;IACtD,IAAID,OAAO,KAAK3B,GAAG,KAAK,GAAG,IAAIA,GAAG,KAAK,QAAQ,IAAIyB,QAAQ,IAAIzB,GAAG,KAAK,QAAQ,IAAIwB,MAAM,CAAC3B,IAAI,KAAK,QAAQ,CAAC,EAAE;MAC5G;IACF;IACA,IAAI,CAAC4B,QAAQ,IAAI,CAACC,UAAU,EAAE;MAC5B;IACF;IACA,IAAI1B,GAAG,KAAK,KAAK,KAAK,CAACjB,OAAO,CAACb,OAAO,IAAI,CAACG,IAAI,CAAC,EAAE;MAChD;IACF;IACAiB,eAAe,CAACpB,OAAO,GAAGyB,KAAK,CAACE,IAAI;IACpC,MAAMgC,IAAI,GAAG;MACX/B,aAAa,EAAEH,KAAK;MACpBC,MAAM,EAAED,KAAK,CAACE;IAChB,CAAC;IACD,QAAQG,GAAG;MACT,KAAK,SAAS;QACZ;UACE,MAAM8B,IAAI,GAAGlB,mBAAmB,CAACY,MAAM,EAAE,CAAC,CAAC,CAAC;UAC5C,IAAIM,IAAI,IAAIA,IAAI,CAACvB,KAAK,EAAEuB,IAAI,CAACvB,KAAK,CAAC,CAAC;UACpCZ,KAAK,CAACoC,cAAc,CAAC,CAAC;UACtB;QACF;MACF,KAAK,WAAW;QACdpC,KAAK,CAACoC,cAAc,CAAC,CAAC;QACtB,IAAI,CAAC1D,IAAI,EAAE;UACTG,QAAQ,CAAC,IAAI,EAAEqD,IAAI,CAAC;QACtB,CAAC,MAAM;UACL,MAAMC,IAAI,GAAGlB,mBAAmB,CAACY,MAAM,EAAE,CAAC,CAAC;UAC3C,IAAIM,IAAI,IAAIA,IAAI,CAACvB,KAAK,EAAEuB,IAAI,CAACvB,KAAK,CAAC,CAAC;QACtC;QACA;MACF,KAAK,KAAK;QACR;QACA;QACA;QACA;QACAhE,gBAAgB,CAACiF,MAAM,CAACpB,aAAa,EAAE,OAAO,EAAE4B,CAAC,IAAI;UACnD,IAAIC,iBAAiB;UACrB,IAAID,CAAC,CAAChC,GAAG,KAAK,KAAK,IAAI,CAACgC,CAAC,CAACR,MAAM,IAAI,EAAE,CAACS,iBAAiB,GAAGlD,OAAO,CAACb,OAAO,KAAK,IAAI,IAAI+D,iBAAiB,CAAC9B,QAAQ,CAAC6B,CAAC,CAACR,MAAM,CAAC,CAAC,EAAE;YAC5HhD,QAAQ,CAAC,KAAK,EAAEqD,IAAI,CAAC;UACvB;QACF,CAAC,EAAE;UACDK,IAAI,EAAE;QACR,CAAC,CAAC;QACF;MACF,KAAK,QAAQ;QACX,IAAIlC,GAAG,KAAK,QAAQ,EAAE;UACpBL,KAAK,CAACoC,cAAc,CAAC,CAAC;UACtBpC,KAAK,CAACwC,eAAe,CAAC,CAAC;QACzB;QACA3D,QAAQ,CAAC,KAAK,EAAEqD,IAAI,CAAC;QACrB;MACF;IACF;EACF,CAAC,CAAC;EACF,OAAO,aAAajE,IAAI,CAACJ,iBAAiB,CAAC4E,QAAQ,EAAE;IACnDC,KAAK,EAAEtC,YAAY;IACnBlB,QAAQ,EAAE,aAAajB,IAAI,CAACT,eAAe,CAACiF,QAAQ,EAAE;MACpDC,KAAK,EAAEnC,OAAO;MACdrB,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC;AACJ;AACAV,QAAQ,CAACmE,WAAW,GAAG,UAAU;AACjCnE,QAAQ,CAACoE,IAAI,GAAGnF,YAAY;AAC5Be,QAAQ,CAACqE,MAAM,GAAGnF,cAAc;AAChCc,QAAQ,CAACsE,IAAI,GAAGlF,YAAY;AAC5B,eAAeY,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}