{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/TradingViewChart.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport './TradingViewChart.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TradingViewChart = ({\n  symbol = 'ES',\n  interval = 'D',\n  theme = 'light',\n  pricelevels = [],\n  savedChartId = null,\n  username = null\n}) => {\n  _s();\n  const containerRef = useRef(null);\n  const scriptRef = useRef(null);\n  const widgetRef = useRef(null);\n\n  // Cleanup function to remove script and widget\n  const cleanup = () => {\n    if (scriptRef.current) {\n      document.body.removeChild(scriptRef.current);\n      scriptRef.current = null;\n    }\n    if (widgetRef.current) {\n      widgetRef.current = null;\n    }\n\n    // Remove any TradingView related elements\n    const tvElements = document.querySelectorAll('iframe[src*=\"tradingview.com\"]');\n    tvElements.forEach(el => el.remove());\n  };\n  useEffect(() => {\n    // Only create widget if container is available\n    if (!containerRef.current) return;\n\n    // Clean up any existing scripts or widgets\n    cleanup();\n\n    // Create script element\n    const script = document.createElement('script');\n    script.type = 'text/javascript';\n    script.src = 'https://s3.tradingview.com/tv.js';\n    script.async = true;\n    script.onload = () => {\n      if (typeof window.TradingView !== 'undefined') {\n        // Base configuration for widget\n        const widgetOptions = {\n          autosize: true,\n          interval: interval,\n          timezone: \"America/New_York\",\n          theme: theme,\n          style: \"1\",\n          locale: \"en\",\n          toolbar_bg: theme === 'dark' ? '#1e1e1e' : '#f1f3f6',\n          enable_publishing: false,\n          allow_symbol_change: true,\n          container_id: 'tradingview_chart',\n          hide_side_toolbar: false,\n          studies: ['RSI@tv-basicstudies'],\n          show_popup_button: true,\n          popup_width: \"1000\",\n          popup_height: \"650\",\n          save_image: true,\n          studies_overrides: {},\n          overrides: {\n            \"mainSeriesProperties.style\": 1,\n            \"paneProperties.background\": theme === 'dark' ? '#1e1e1e' : '#ffffff',\n            \"paneProperties.vertGridProperties.color\": theme === 'dark' ? '#333333' : '#e0e0e0',\n            \"paneProperties.horzGridProperties.color\": theme === 'dark' ? '#333333' : '#e0e0e0',\n            \"symbolWatermarkProperties.transparency\": 90,\n            \"scalesProperties.textColor\": theme === 'dark' ? '#aaa' : '#333'\n          },\n          loading_screen: {\n            backgroundColor: theme === 'dark' ? '#1e1e1e' : '#ffffff'\n          },\n          disabled_features: [\"header_symbol_search\", \"header_compare\"],\n          enabled_features: [\"use_localstorage_for_settings\", \"save_chart_properties_to_local_storage\"]\n        };\n\n        // If we have a saved chart ID and username, use that instead of the symbol\n        if (savedChartId && username) {\n          widgetOptions.chart = savedChartId;\n          widgetOptions.username = username;\n        } else {\n          // Otherwise use the provided symbol\n          widgetOptions.symbol = `${getSymbolPrefix(symbol)}:${symbol}`;\n        }\n\n        // Create the widget\n        // @ts-ignore\n        widgetRef.current = new window.TradingView.widget(widgetOptions);\n      }\n    };\n\n    // Add script to document\n    document.body.appendChild(script);\n    scriptRef.current = script;\n\n    // Cleanup on unmount\n    return cleanup;\n  }, [symbol, interval, theme, savedChartId, username]);\n\n  // Draw price levels when the widget is loaded and ready\n  useEffect(() => {\n    if (!widgetRef.current || !pricelevels.length) return;\n    const drawPriceLevels = () => {\n      // Wait for the widget to be ready\n      if (widgetRef.current && widgetRef.current.chart && widgetRef.current.chart()) {\n        const chart = widgetRef.current.chart();\n\n        // Clear existing lines\n        chart.removeAllShapes();\n\n        // Add each price level\n        pricelevels.forEach(level => {\n          chart.createMultipointShape([{\n            time: chart.getVisibleRange().from,\n            price: level.price\n          }, {\n            time: chart.getVisibleRange().to,\n            price: level.price\n          }], {\n            shape: 'horizontal_line',\n            lock: true,\n            disableSelection: true,\n            disableSave: true,\n            disableUndo: true,\n            overrides: {\n              linecolor: level.color || (level.type === 'support' ? '#4CAF50' : '#F44336'),\n              linewidth: level.width || 2,\n              linestyle: level.style || 1,\n              // 0-solid, 1-dotted, 2-dashed\n              showLabel: true,\n              text: level.label || (level.type === 'support' ? 'Support' : 'Resistance'),\n              textcolor: level.textColor || (theme === 'dark' ? '#fff' : '#333')\n            }\n          });\n        });\n      } else {\n        // If not ready, try again in 500ms\n        setTimeout(drawPriceLevels, 500);\n      }\n    };\n\n    // Start the process\n    setTimeout(drawPriceLevels, 1000);\n  }, [pricelevels, widgetRef.current, theme]);\n\n  // Helper function to get the correct symbol prefix based on the symbol\n  const getSymbolPrefix = symbol => {\n    const prefixes = {\n      'ES': 'CME_MINI',\n      // E-mini S&P 500\n      'NQ': 'CME_MINI',\n      // E-mini NASDAQ\n      'YM': 'CME_MINI',\n      // E-mini Dow\n      'RTY': 'CME_MINI',\n      // E-mini Russell 2000\n      'VIX': 'CBOE',\n      // VIX index\n      'VX': 'CFE',\n      // VIX futures\n      'GC': 'COMEX',\n      // Gold futures\n      'SI': 'COMEX',\n      // Silver futures\n      'CL': 'NYMEX',\n      // Crude Oil futures\n      'NG': 'NYMEX',\n      // Natural Gas futures\n      'ZB': 'CBOT',\n      // 30-Year U.S. Treasury Bond futures\n      'ZN': 'CBOT',\n      // 10-Year U.S. Treasury Note futures\n      'ZF': 'CBOT',\n      // 5-Year U.S. Treasury Note futures\n      'ZT': 'CBOT',\n      // 2-Year U.S. Treasury Note futures\n      'default': 'CME' // Default prefix\n    };\n    return prefixes[symbol] || prefixes['default'];\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"tradingview-chart-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      id: \"tradingview_chart\",\n      ref: containerRef,\n      className: \"tradingview-chart\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 170,\n    columnNumber: 5\n  }, this);\n};\n_s(TradingViewChart, \"jTJuzGFopi/sQUzM8sriKCcDzPY=\");\n_c = TradingViewChart;\nexport default TradingViewChart;\nvar _c;\n$RefreshReg$(_c, \"TradingViewChart\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "jsxDEV", "_jsxDEV", "TradingViewChart", "symbol", "interval", "theme", "pricelevels", "savedChartId", "username", "_s", "containerRef", "scriptRef", "widgetRef", "cleanup", "current", "document", "body", "<PERSON><PERSON><PERSON><PERSON>", "tvElements", "querySelectorAll", "for<PERSON>ach", "el", "remove", "script", "createElement", "type", "src", "async", "onload", "window", "TradingView", "widgetOptions", "autosize", "timezone", "style", "locale", "toolbar_bg", "enable_publishing", "allow_symbol_change", "container_id", "hide_side_toolbar", "studies", "show_popup_button", "popup_width", "popup_height", "save_image", "studies_overrides", "overrides", "loading_screen", "backgroundColor", "disabled_features", "enabled_features", "chart", "getSymbolPrefix", "widget", "append<PERSON><PERSON><PERSON>", "length", "drawPriceLevels", "removeAllShapes", "level", "createMultipointShape", "time", "getVisibleRange", "from", "price", "to", "shape", "lock", "disableSelection", "disableSave", "disableUndo", "linecolor", "color", "linewidth", "width", "linestyle", "showLabel", "text", "label", "textcolor", "textColor", "setTimeout", "prefixes", "className", "children", "id", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/TradingViewChart.js"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport './TradingViewChart.css';\n\nconst TradingViewChart = ({ symbol = 'ES', interval = 'D', theme = 'light', pricelevels = [], savedChartId = null, username = null }) => {\n  const containerRef = useRef(null);\n  const scriptRef = useRef(null);\n  const widgetRef = useRef(null);\n\n  // Cleanup function to remove script and widget\n  const cleanup = () => {\n    if (scriptRef.current) {\n      document.body.removeChild(scriptRef.current);\n      scriptRef.current = null;\n    }\n\n    if (widgetRef.current) {\n      widgetRef.current = null;\n    }\n\n    // Remove any TradingView related elements\n    const tvElements = document.querySelectorAll('iframe[src*=\"tradingview.com\"]');\n    tvElements.forEach(el => el.remove());\n  };\n\n  useEffect(() => {\n    // Only create widget if container is available\n    if (!containerRef.current) return;\n\n    // Clean up any existing scripts or widgets\n    cleanup();\n\n    // Create script element\n    const script = document.createElement('script');\n    script.type = 'text/javascript';\n    script.src = 'https://s3.tradingview.com/tv.js';\n    script.async = true;\n    script.onload = () => {\n      if (typeof window.TradingView !== 'undefined') {\n        // Base configuration for widget\n        const widgetOptions = {\n          autosize: true,\n          interval: interval,\n          timezone: \"America/New_York\",\n          theme: theme,\n          style: \"1\",\n          locale: \"en\",\n          toolbar_bg: theme === 'dark' ? '#1e1e1e' : '#f1f3f6',\n          enable_publishing: false,\n          allow_symbol_change: true,\n          container_id: 'tradingview_chart',\n          hide_side_toolbar: false,\n          studies: ['RSI@tv-basicstudies'],\n          show_popup_button: true,\n          popup_width: \"1000\",\n          popup_height: \"650\",\n          save_image: true,\n          studies_overrides: {},\n          overrides: {\n            \"mainSeriesProperties.style\": 1,\n            \"paneProperties.background\": theme === 'dark' ? '#1e1e1e' : '#ffffff',\n            \"paneProperties.vertGridProperties.color\": theme === 'dark' ? '#333333' : '#e0e0e0',\n            \"paneProperties.horzGridProperties.color\": theme === 'dark' ? '#333333' : '#e0e0e0',\n            \"symbolWatermarkProperties.transparency\": 90,\n            \"scalesProperties.textColor\": theme === 'dark' ? '#aaa' : '#333',\n          },\n          loading_screen: { backgroundColor: theme === 'dark' ? '#1e1e1e' : '#ffffff' },\n          disabled_features: [\n            \"header_symbol_search\",\n            \"header_compare\",\n          ],\n          enabled_features: [\n            \"use_localstorage_for_settings\",\n            \"save_chart_properties_to_local_storage\"\n          ],\n        };\n\n        // If we have a saved chart ID and username, use that instead of the symbol\n        if (savedChartId && username) {\n          widgetOptions.chart = savedChartId;\n          widgetOptions.username = username;\n        } else {\n          // Otherwise use the provided symbol\n          widgetOptions.symbol = `${getSymbolPrefix(symbol)}:${symbol}`;\n        }\n\n        // Create the widget\n        // @ts-ignore\n        widgetRef.current = new window.TradingView.widget(widgetOptions);\n      }\n    };\n\n    // Add script to document\n    document.body.appendChild(script);\n    scriptRef.current = script;\n\n    // Cleanup on unmount\n    return cleanup;\n  }, [symbol, interval, theme, savedChartId, username]);\n\n  // Draw price levels when the widget is loaded and ready\n  useEffect(() => {\n    if (!widgetRef.current || !pricelevels.length) return;\n\n    const drawPriceLevels = () => {\n      // Wait for the widget to be ready\n      if (widgetRef.current && widgetRef.current.chart && widgetRef.current.chart()) {\n        const chart = widgetRef.current.chart();\n\n        // Clear existing lines\n        chart.removeAllShapes();\n\n        // Add each price level\n        pricelevels.forEach(level => {\n          chart.createMultipointShape(\n            [\n              { time: chart.getVisibleRange().from, price: level.price },\n              { time: chart.getVisibleRange().to, price: level.price }\n            ],\n            {\n              shape: 'horizontal_line',\n              lock: true,\n              disableSelection: true,\n              disableSave: true,\n              disableUndo: true,\n              overrides: {\n                linecolor: level.color || (level.type === 'support' ? '#4CAF50' : '#F44336'),\n                linewidth: level.width || 2,\n                linestyle: level.style || 1, // 0-solid, 1-dotted, 2-dashed\n                showLabel: true,\n                text: level.label || (level.type === 'support' ? 'Support' : 'Resistance'),\n                textcolor: level.textColor || (theme === 'dark' ? '#fff' : '#333'),\n              }\n            }\n          );\n        });\n      } else {\n        // If not ready, try again in 500ms\n        setTimeout(drawPriceLevels, 500);\n      }\n    };\n\n    // Start the process\n    setTimeout(drawPriceLevels, 1000);\n  }, [pricelevels, widgetRef.current, theme]);\n\n  // Helper function to get the correct symbol prefix based on the symbol\n  const getSymbolPrefix = (symbol) => {\n    const prefixes = {\n      'ES': 'CME_MINI', // E-mini S&P 500\n      'NQ': 'CME_MINI', // E-mini NASDAQ\n      'YM': 'CME_MINI', // E-mini Dow\n      'RTY': 'CME_MINI', // E-mini Russell 2000\n      'VIX': 'CBOE',     // VIX index\n      'VX': 'CFE',       // VIX futures\n      'GC': 'COMEX',     // Gold futures\n      'SI': 'COMEX',     // Silver futures\n      'CL': 'NYMEX',     // Crude Oil futures\n      'NG': 'NYMEX',     // Natural Gas futures\n      'ZB': 'CBOT',      // 30-Year U.S. Treasury Bond futures\n      'ZN': 'CBOT',      // 10-Year U.S. Treasury Note futures\n      'ZF': 'CBOT',      // 5-Year U.S. Treasury Note futures\n      'ZT': 'CBOT',      // 2-Year U.S. Treasury Note futures\n      'default': 'CME'   // Default prefix\n    };\n\n    return prefixes[symbol] || prefixes['default'];\n  };\n\n  return (\n    <div className=\"tradingview-chart-container\">\n      <div id=\"tradingview_chart\" ref={containerRef} className=\"tradingview-chart\"></div>\n    </div>\n  );\n};\n\nexport default TradingViewChart;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,gBAAgB,GAAGA,CAAC;EAAEC,MAAM,GAAG,IAAI;EAAEC,QAAQ,GAAG,GAAG;EAAEC,KAAK,GAAG,OAAO;EAAEC,WAAW,GAAG,EAAE;EAAEC,YAAY,GAAG,IAAI;EAAEC,QAAQ,GAAG;AAAK,CAAC,KAAK;EAAAC,EAAA;EACvI,MAAMC,YAAY,GAAGZ,MAAM,CAAC,IAAI,CAAC;EACjC,MAAMa,SAAS,GAAGb,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMc,SAAS,GAAGd,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAMe,OAAO,GAAGA,CAAA,KAAM;IACpB,IAAIF,SAAS,CAACG,OAAO,EAAE;MACrBC,QAAQ,CAACC,IAAI,CAACC,WAAW,CAACN,SAAS,CAACG,OAAO,CAAC;MAC5CH,SAAS,CAACG,OAAO,GAAG,IAAI;IAC1B;IAEA,IAAIF,SAAS,CAACE,OAAO,EAAE;MACrBF,SAAS,CAACE,OAAO,GAAG,IAAI;IAC1B;;IAEA;IACA,MAAMI,UAAU,GAAGH,QAAQ,CAACI,gBAAgB,CAAC,gCAAgC,CAAC;IAC9ED,UAAU,CAACE,OAAO,CAACC,EAAE,IAAIA,EAAE,CAACC,MAAM,CAAC,CAAC,CAAC;EACvC,CAAC;EAEDzB,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACa,YAAY,CAACI,OAAO,EAAE;;IAE3B;IACAD,OAAO,CAAC,CAAC;;IAET;IACA,MAAMU,MAAM,GAAGR,QAAQ,CAACS,aAAa,CAAC,QAAQ,CAAC;IAC/CD,MAAM,CAACE,IAAI,GAAG,iBAAiB;IAC/BF,MAAM,CAACG,GAAG,GAAG,kCAAkC;IAC/CH,MAAM,CAACI,KAAK,GAAG,IAAI;IACnBJ,MAAM,CAACK,MAAM,GAAG,MAAM;MACpB,IAAI,OAAOC,MAAM,CAACC,WAAW,KAAK,WAAW,EAAE;QAC7C;QACA,MAAMC,aAAa,GAAG;UACpBC,QAAQ,EAAE,IAAI;UACd5B,QAAQ,EAAEA,QAAQ;UAClB6B,QAAQ,EAAE,kBAAkB;UAC5B5B,KAAK,EAAEA,KAAK;UACZ6B,KAAK,EAAE,GAAG;UACVC,MAAM,EAAE,IAAI;UACZC,UAAU,EAAE/B,KAAK,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS;UACpDgC,iBAAiB,EAAE,KAAK;UACxBC,mBAAmB,EAAE,IAAI;UACzBC,YAAY,EAAE,mBAAmB;UACjCC,iBAAiB,EAAE,KAAK;UACxBC,OAAO,EAAE,CAAC,qBAAqB,CAAC;UAChCC,iBAAiB,EAAE,IAAI;UACvBC,WAAW,EAAE,MAAM;UACnBC,YAAY,EAAE,KAAK;UACnBC,UAAU,EAAE,IAAI;UAChBC,iBAAiB,EAAE,CAAC,CAAC;UACrBC,SAAS,EAAE;YACT,4BAA4B,EAAE,CAAC;YAC/B,2BAA2B,EAAE1C,KAAK,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS;YACrE,yCAAyC,EAAEA,KAAK,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS;YACnF,yCAAyC,EAAEA,KAAK,KAAK,MAAM,GAAG,SAAS,GAAG,SAAS;YACnF,wCAAwC,EAAE,EAAE;YAC5C,4BAA4B,EAAEA,KAAK,KAAK,MAAM,GAAG,MAAM,GAAG;UAC5D,CAAC;UACD2C,cAAc,EAAE;YAAEC,eAAe,EAAE5C,KAAK,KAAK,MAAM,GAAG,SAAS,GAAG;UAAU,CAAC;UAC7E6C,iBAAiB,EAAE,CACjB,sBAAsB,EACtB,gBAAgB,CACjB;UACDC,gBAAgB,EAAE,CAChB,+BAA+B,EAC/B,wCAAwC;QAE5C,CAAC;;QAED;QACA,IAAI5C,YAAY,IAAIC,QAAQ,EAAE;UAC5BuB,aAAa,CAACqB,KAAK,GAAG7C,YAAY;UAClCwB,aAAa,CAACvB,QAAQ,GAAGA,QAAQ;QACnC,CAAC,MAAM;UACL;UACAuB,aAAa,CAAC5B,MAAM,GAAG,GAAGkD,eAAe,CAAClD,MAAM,CAAC,IAAIA,MAAM,EAAE;QAC/D;;QAEA;QACA;QACAS,SAAS,CAACE,OAAO,GAAG,IAAIe,MAAM,CAACC,WAAW,CAACwB,MAAM,CAACvB,aAAa,CAAC;MAClE;IACF,CAAC;;IAED;IACAhB,QAAQ,CAACC,IAAI,CAACuC,WAAW,CAAChC,MAAM,CAAC;IACjCZ,SAAS,CAACG,OAAO,GAAGS,MAAM;;IAE1B;IACA,OAAOV,OAAO;EAChB,CAAC,EAAE,CAACV,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEE,YAAY,EAAEC,QAAQ,CAAC,CAAC;;EAErD;EACAX,SAAS,CAAC,MAAM;IACd,IAAI,CAACe,SAAS,CAACE,OAAO,IAAI,CAACR,WAAW,CAACkD,MAAM,EAAE;IAE/C,MAAMC,eAAe,GAAGA,CAAA,KAAM;MAC5B;MACA,IAAI7C,SAAS,CAACE,OAAO,IAAIF,SAAS,CAACE,OAAO,CAACsC,KAAK,IAAIxC,SAAS,CAACE,OAAO,CAACsC,KAAK,CAAC,CAAC,EAAE;QAC7E,MAAMA,KAAK,GAAGxC,SAAS,CAACE,OAAO,CAACsC,KAAK,CAAC,CAAC;;QAEvC;QACAA,KAAK,CAACM,eAAe,CAAC,CAAC;;QAEvB;QACApD,WAAW,CAACc,OAAO,CAACuC,KAAK,IAAI;UAC3BP,KAAK,CAACQ,qBAAqB,CACzB,CACE;YAAEC,IAAI,EAAET,KAAK,CAACU,eAAe,CAAC,CAAC,CAACC,IAAI;YAAEC,KAAK,EAAEL,KAAK,CAACK;UAAM,CAAC,EAC1D;YAAEH,IAAI,EAAET,KAAK,CAACU,eAAe,CAAC,CAAC,CAACG,EAAE;YAAED,KAAK,EAAEL,KAAK,CAACK;UAAM,CAAC,CACzD,EACD;YACEE,KAAK,EAAE,iBAAiB;YACxBC,IAAI,EAAE,IAAI;YACVC,gBAAgB,EAAE,IAAI;YACtBC,WAAW,EAAE,IAAI;YACjBC,WAAW,EAAE,IAAI;YACjBvB,SAAS,EAAE;cACTwB,SAAS,EAAEZ,KAAK,CAACa,KAAK,KAAKb,KAAK,CAAClC,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC;cAC5EgD,SAAS,EAAEd,KAAK,CAACe,KAAK,IAAI,CAAC;cAC3BC,SAAS,EAAEhB,KAAK,CAACzB,KAAK,IAAI,CAAC;cAAE;cAC7B0C,SAAS,EAAE,IAAI;cACfC,IAAI,EAAElB,KAAK,CAACmB,KAAK,KAAKnB,KAAK,CAAClC,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,YAAY,CAAC;cAC1EsD,SAAS,EAAEpB,KAAK,CAACqB,SAAS,KAAK3E,KAAK,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM;YACnE;UACF,CACF,CAAC;QACH,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACA4E,UAAU,CAACxB,eAAe,EAAE,GAAG,CAAC;MAClC;IACF,CAAC;;IAED;IACAwB,UAAU,CAACxB,eAAe,EAAE,IAAI,CAAC;EACnC,CAAC,EAAE,CAACnD,WAAW,EAAEM,SAAS,CAACE,OAAO,EAAET,KAAK,CAAC,CAAC;;EAE3C;EACA,MAAMgD,eAAe,GAAIlD,MAAM,IAAK;IAClC,MAAM+E,QAAQ,GAAG;MACf,IAAI,EAAE,UAAU;MAAE;MAClB,IAAI,EAAE,UAAU;MAAE;MAClB,IAAI,EAAE,UAAU;MAAE;MAClB,KAAK,EAAE,UAAU;MAAE;MACnB,KAAK,EAAE,MAAM;MAAM;MACnB,IAAI,EAAE,KAAK;MAAQ;MACnB,IAAI,EAAE,OAAO;MAAM;MACnB,IAAI,EAAE,OAAO;MAAM;MACnB,IAAI,EAAE,OAAO;MAAM;MACnB,IAAI,EAAE,OAAO;MAAM;MACnB,IAAI,EAAE,MAAM;MAAO;MACnB,IAAI,EAAE,MAAM;MAAO;MACnB,IAAI,EAAE,MAAM;MAAO;MACnB,IAAI,EAAE,MAAM;MAAO;MACnB,SAAS,EAAE,KAAK,CAAG;IACrB,CAAC;IAED,OAAOA,QAAQ,CAAC/E,MAAM,CAAC,IAAI+E,QAAQ,CAAC,SAAS,CAAC;EAChD,CAAC;EAED,oBACEjF,OAAA;IAAKkF,SAAS,EAAC,6BAA6B;IAAAC,QAAA,eAC1CnF,OAAA;MAAKoF,EAAE,EAAC,mBAAmB;MAACC,GAAG,EAAE5E,YAAa;MAACyE,SAAS,EAAC;IAAmB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChF,CAAC;AAEV,CAAC;AAACjF,EAAA,CA1KIP,gBAAgB;AAAAyF,EAAA,GAAhBzF,gBAAgB;AA4KtB,eAAeA,gBAAgB;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}