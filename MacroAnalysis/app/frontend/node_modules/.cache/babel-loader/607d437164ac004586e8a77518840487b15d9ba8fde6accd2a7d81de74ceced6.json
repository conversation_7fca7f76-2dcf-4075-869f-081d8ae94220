{"ast": null, "code": "/* eslint-disable no-return-assign */\nimport canUseDOM from './canUseDOM';\nexport var optionsSupported = false;\nexport var onceSupported = false;\ntry {\n  var options = {\n    get passive() {\n      return optionsSupported = true;\n    },\n    get once() {\n      // eslint-disable-next-line no-multi-assign\n      return onceSupported = optionsSupported = true;\n    }\n  };\n  if (canUseDOM) {\n    window.addEventListener('test', options, options);\n    window.removeEventListener('test', options, true);\n  }\n} catch (e) {\n  /* */\n}\n\n/**\n * An `addEventListener` ponyfill, supports the `once` option\n * \n * @param node the element\n * @param eventName the event name\n * @param handle the handler\n * @param options event options\n */\nfunction addEventListener(node, eventName, handler, options) {\n  if (options && typeof options !== 'boolean' && !onceSupported) {\n    var once = options.once,\n      capture = options.capture;\n    var wrappedHandler = handler;\n    if (!onceSupported && once) {\n      wrappedHandler = handler.__once || function onceHandler(event) {\n        this.removeEventListener(eventName, onceHandler, capture);\n        handler.call(this, event);\n      };\n      handler.__once = wrappedHandler;\n    }\n    node.addEventListener(eventName, wrappedHandler, optionsSupported ? options : capture);\n  }\n  node.addEventListener(eventName, handler, options);\n}\nexport default addEventListener;", "map": {"version": 3, "names": ["canUseDOM", "optionsSupported", "onceSupported", "options", "passive", "once", "window", "addEventListener", "removeEventListener", "e", "node", "eventName", "handler", "capture", "wrapped<PERSON>andler", "__once", "once<PERSON><PERSON><PERSON>", "event", "call"], "sources": ["/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/node_modules/dom-helpers/esm/addEventListener.js"], "sourcesContent": ["/* eslint-disable no-return-assign */\nimport canUseDOM from './canUseDOM';\nexport var optionsSupported = false;\nexport var onceSupported = false;\n\ntry {\n  var options = {\n    get passive() {\n      return optionsSupported = true;\n    },\n\n    get once() {\n      // eslint-disable-next-line no-multi-assign\n      return onceSupported = optionsSupported = true;\n    }\n\n  };\n\n  if (canUseDOM) {\n    window.addEventListener('test', options, options);\n    window.removeEventListener('test', options, true);\n  }\n} catch (e) {\n  /* */\n}\n\n/**\n * An `addEventListener` ponyfill, supports the `once` option\n * \n * @param node the element\n * @param eventName the event name\n * @param handle the handler\n * @param options event options\n */\nfunction addEventListener(node, eventName, handler, options) {\n  if (options && typeof options !== 'boolean' && !onceSupported) {\n    var once = options.once,\n        capture = options.capture;\n    var wrappedHandler = handler;\n\n    if (!onceSupported && once) {\n      wrappedHandler = handler.__once || function onceHandler(event) {\n        this.removeEventListener(eventName, onceHandler, capture);\n        handler.call(this, event);\n      };\n\n      handler.__once = wrappedHandler;\n    }\n\n    node.addEventListener(eventName, wrappedHandler, optionsSupported ? options : capture);\n  }\n\n  node.addEventListener(eventName, handler, options);\n}\n\nexport default addEventListener;"], "mappings": "AAAA;AACA,OAAOA,SAAS,MAAM,aAAa;AACnC,OAAO,IAAIC,gBAAgB,GAAG,KAAK;AACnC,OAAO,IAAIC,aAAa,GAAG,KAAK;AAEhC,IAAI;EACF,IAAIC,OAAO,GAAG;IACZ,IAAIC,OAAOA,CAAA,EAAG;MACZ,OAAOH,gBAAgB,GAAG,IAAI;IAChC,CAAC;IAED,IAAII,IAAIA,CAAA,EAAG;MACT;MACA,OAAOH,aAAa,GAAGD,gBAAgB,GAAG,IAAI;IAChD;EAEF,CAAC;EAED,IAAID,SAAS,EAAE;IACbM,MAAM,CAACC,gBAAgB,CAAC,MAAM,EAAEJ,OAAO,EAAEA,OAAO,CAAC;IACjDG,MAAM,CAACE,mBAAmB,CAAC,MAAM,EAAEL,OAAO,EAAE,IAAI,CAAC;EACnD;AACF,CAAC,CAAC,OAAOM,CAAC,EAAE;EACV;AAAA;;AAGF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASF,gBAAgBA,CAACG,IAAI,EAAEC,SAAS,EAAEC,OAAO,EAAET,OAAO,EAAE;EAC3D,IAAIA,OAAO,IAAI,OAAOA,OAAO,KAAK,SAAS,IAAI,CAACD,aAAa,EAAE;IAC7D,IAAIG,IAAI,GAAGF,OAAO,CAACE,IAAI;MACnBQ,OAAO,GAAGV,OAAO,CAACU,OAAO;IAC7B,IAAIC,cAAc,GAAGF,OAAO;IAE5B,IAAI,CAACV,aAAa,IAAIG,IAAI,EAAE;MAC1BS,cAAc,GAAGF,OAAO,CAACG,MAAM,IAAI,SAASC,WAAWA,CAACC,KAAK,EAAE;QAC7D,IAAI,CAACT,mBAAmB,CAACG,SAAS,EAAEK,WAAW,EAAEH,OAAO,CAAC;QACzDD,OAAO,CAACM,IAAI,CAAC,IAAI,EAAED,KAAK,CAAC;MAC3B,CAAC;MAEDL,OAAO,CAACG,MAAM,GAAGD,cAAc;IACjC;IAEAJ,IAAI,CAACH,gBAAgB,CAACI,SAAS,EAAEG,cAAc,EAAEb,gBAAgB,GAAGE,OAAO,GAAGU,OAAO,CAAC;EACxF;EAEAH,IAAI,CAACH,gBAAgB,CAACI,SAAS,EAAEC,OAAO,EAAET,OAAO,CAAC;AACpD;AAEA,eAAeI,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}