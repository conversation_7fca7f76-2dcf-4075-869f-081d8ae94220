{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport divWithClassName from './divWithClassName';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH4 = divWithClassName('h4');\nconst ModalTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH4,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'modal-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nModalTitle.displayName = 'ModalTitle';\nexport default ModalTitle;", "map": {"version": 3, "names": ["React", "classNames", "divWithClassName", "useBootstrapPrefix", "jsx", "_jsx", "DivStyledAsH4", "ModalTitle", "forwardRef", "className", "bsPrefix", "as", "Component", "props", "ref", "displayName"], "sources": ["/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/node_modules/react-bootstrap/esm/ModalTitle.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport divWithClassName from './divWithClassName';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH4 = divWithClassName('h4');\nconst ModalTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH4,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'modal-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nModalTitle.displayName = 'ModalTitle';\nexport default ModalTitle;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,kBAAkB,QAAQ,iBAAiB;AACpD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,aAAa,GAAGJ,gBAAgB,CAAC,IAAI,CAAC;AAC5C,MAAMK,UAAU,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,CAAC;EAChDC,SAAS;EACTC,QAAQ;EACRC,EAAE,EAAEC,SAAS,GAAGN,aAAa;EAC7B,GAAGO;AACL,CAAC,EAAEC,GAAG,KAAK;EACTJ,QAAQ,GAAGP,kBAAkB,CAACO,QAAQ,EAAE,aAAa,CAAC;EACtD,OAAO,aAAaL,IAAI,CAACO,SAAS,EAAE;IAClCE,GAAG,EAAEA,GAAG;IACRL,SAAS,EAAER,UAAU,CAACQ,SAAS,EAAEC,QAAQ,CAAC;IAC1C,GAAGG;EACL,CAAC,CAAC;AACJ,CAAC,CAAC;AACFN,UAAU,CAACQ,WAAW,GAAG,YAAY;AACrC,eAAeR,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}