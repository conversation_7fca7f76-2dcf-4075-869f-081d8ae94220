{"ast": null, "code": "\"use client\";\n\nimport * as React from 'react';\n\n// TODO: check\n\nconst context = /*#__PURE__*/React.createContext(null);\ncontext.displayName = 'NavbarContext';\nexport default context;", "map": {"version": 3, "names": ["React", "context", "createContext", "displayName"], "sources": ["/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/node_modules/react-bootstrap/esm/NavbarContext.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\n\n// TODO: check\n\nconst context = /*#__PURE__*/React.createContext(null);\ncontext.displayName = 'NavbarContext';\nexport default context;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;;AAE9B;;AAEA,MAAMC,OAAO,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AACtDD,OAAO,CAACE,WAAW,GAAG,eAAe;AACrC,eAAeF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}