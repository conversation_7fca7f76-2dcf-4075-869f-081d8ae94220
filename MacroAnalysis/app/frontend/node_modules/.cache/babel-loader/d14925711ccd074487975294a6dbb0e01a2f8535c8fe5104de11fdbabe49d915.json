{"ast": null, "code": "import * as React from 'react';\nconst SelectableContext = /*#__PURE__*/React.createContext(null);\nexport const makeEventKey = (eventKey, href = null) => {\n  if (eventKey != null) return String(eventKey);\n  return href || null;\n};\nexport default SelectableContext;", "map": {"version": 3, "names": ["React", "SelectableContext", "createContext", "makeEventKey", "eventKey", "href", "String"], "sources": ["/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/node_modules/@restart/ui/esm/SelectableContext.js"], "sourcesContent": ["import * as React from 'react';\nconst SelectableContext = /*#__PURE__*/React.createContext(null);\nexport const makeEventKey = (eventKey, href = null) => {\n  if (eventKey != null) return String(eventKey);\n  return href || null;\n};\nexport default SelectableContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,iBAAiB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAChE,OAAO,MAAMC,YAAY,GAAGA,CAACC,QAAQ,EAAEC,IAAI,GAAG,IAAI,KAAK;EACrD,IAAID,QAAQ,IAAI,IAAI,EAAE,OAAOE,MAAM,CAACF,QAAQ,CAAC;EAC7C,OAAOC,IAAI,IAAI,IAAI;AACrB,CAAC;AACD,eAAeJ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}