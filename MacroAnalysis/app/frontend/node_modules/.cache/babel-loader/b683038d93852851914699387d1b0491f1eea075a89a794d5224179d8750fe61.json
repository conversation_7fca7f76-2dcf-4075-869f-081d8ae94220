{"ast": null, "code": "/* eslint-disable no-bitwise, no-cond-assign */\n\n/**\n * Checks if an element contains another given element.\n * \n * @param context the context element\n * @param node the element to check\n */\nexport default function contains(context, node) {\n  // HTML DOM and SVG DOM may have different support levels,\n  // so we need to check on context instead of a document root element.\n  if (context.contains) return context.contains(node);\n  if (context.compareDocumentPosition) return context === node || !!(context.compareDocumentPosition(node) & 16);\n}", "map": {"version": 3, "names": ["contains", "context", "node", "compareDocumentPosition"], "sources": ["/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/node_modules/dom-helpers/esm/contains.js"], "sourcesContent": ["/* eslint-disable no-bitwise, no-cond-assign */\n\n/**\n * Checks if an element contains another given element.\n * \n * @param context the context element\n * @param node the element to check\n */\nexport default function contains(context, node) {\n  // HTML DOM and SVG DOM may have different support levels,\n  // so we need to check on context instead of a document root element.\n  if (context.contains) return context.contains(node);\n  if (context.compareDocumentPosition) return context === node || !!(context.compareDocumentPosition(node) & 16);\n}"], "mappings": "AAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,QAAQA,CAACC,OAAO,EAAEC,IAAI,EAAE;EAC9C;EACA;EACA,IAAID,OAAO,CAACD,QAAQ,EAAE,OAAOC,OAAO,CAACD,QAAQ,CAACE,IAAI,CAAC;EACnD,IAAID,OAAO,CAACE,uBAAuB,EAAE,OAAOF,OAAO,KAAKC,IAAI,IAAI,CAAC,EAAED,OAAO,CAACE,uBAAuB,CAACD,IAAI,CAAC,GAAG,EAAE,CAAC;AAChH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}