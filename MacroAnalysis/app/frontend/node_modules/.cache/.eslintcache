[{"/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/index.js": "1", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/reportWebVitals.js": "2", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/App.js": "3", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/pages/Dashboard.js": "4", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/pages/MarketImpact.js": "5", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/pages/EventDetail.js": "6", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/pages/SurpriseFactor.js": "7", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/pages/EventsOverview.js": "8", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/layout/Header.js": "9", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/HistoricalPerformanceChart.js": "10", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/SurpriseFactorChart.js": "11", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/MarketImpactChart.js": "12", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/pages/TestPage.js": "13", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/HistoricalTrendsChart.js": "14", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/pages/ScenarioAnalysis.js": "15", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/ScenarioChart.js": "16", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/pages/TradingStrategy.js": "17", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/pages/DailyFundamentals.js": "18", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/layout/LoadingSpinner.js": "19", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/layout/ErrorAlert.js": "20", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/ESFuturesChart.js": "21", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/VIXFuturesChart.js": "22", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/services/api.js": "23", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/ChartSettings.js": "24", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/TradingViewChartWrapper.js": "25", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/TradingViewChart.js": "26", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/PriceLevelsManager.js": "27", "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/SavedChartGuide.js": "28"}, {"size": 460, "mtime": 1746849765622, "results": "29", "hashOfConfig": "30"}, {"size": 362, "mtime": 1746849784242, "results": "31", "hashOfConfig": "30"}, {"size": 1495, "mtime": 1747237743141, "results": "32", "hashOfConfig": "30"}, {"size": 9248, "mtime": 1747018546059, "results": "33", "hashOfConfig": "30"}, {"size": 11484, "mtime": 1747024206281, "results": "34", "hashOfConfig": "30"}, {"size": 8355, "mtime": 1746850146672, "results": "35", "hashOfConfig": "30"}, {"size": 7882, "mtime": 1747022515871, "results": "36", "hashOfConfig": "30"}, {"size": 16734, "mtime": 1747020951302, "results": "37", "hashOfConfig": "30"}, {"size": 1872, "mtime": 1747237751072, "results": "38", "hashOfConfig": "30"}, {"size": 2600, "mtime": 1747017175668, "results": "39", "hashOfConfig": "30"}, {"size": 3347, "mtime": 1747022098200, "results": "40", "hashOfConfig": "30"}, {"size": 3713, "mtime": 1746850004478, "results": "41", "hashOfConfig": "30"}, {"size": 1539, "mtime": 1747017293248, "results": "42", "hashOfConfig": "30"}, {"size": 3288, "mtime": 1747018533279, "results": "43", "hashOfConfig": "30"}, {"size": 26331, "mtime": 1747195830350, "results": "44", "hashOfConfig": "30"}, {"size": 5661, "mtime": 1747194767432, "results": "45", "hashOfConfig": "30"}, {"size": 5407, "mtime": 1747199165551, "results": "46", "hashOfConfig": "30"}, {"size": 8173, "mtime": 1747240115466, "results": "47", "hashOfConfig": "30"}, {"size": 314, "mtime": 1747237688316, "results": "48", "hashOfConfig": "30"}, {"size": 698, "mtime": 1747237701190, "results": "49", "hashOfConfig": "30"}, {"size": 19162, "mtime": 1747238494180, "results": "50", "hashOfConfig": "30"}, {"size": 20545, "mtime": 1747238595863, "results": "51", "hashOfConfig": "30"}, {"size": 2481, "mtime": 1747237961491, "results": "52", "hashOfConfig": "30"}, {"size": 6703, "mtime": 1747238088885, "results": "53", "hashOfConfig": "30"}, {"size": 6508, "mtime": 1747240539902, "results": "54", "hashOfConfig": "30"}, {"size": 6200, "mtime": 1747240702077, "results": "55", "hashOfConfig": "30"}, {"size": 5529, "mtime": 1747239928397, "results": "56", "hashOfConfig": "30"}, {"size": 3277, "mtime": 1747240507137, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1yej92d", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/index.js", [], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/reportWebVitals.js", [], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/App.js", [], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/pages/Dashboard.js", ["142"], ["143"], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/pages/MarketImpact.js", ["144"], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/pages/EventDetail.js", [], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/pages/SurpriseFactor.js", [], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/pages/EventsOverview.js", [], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/layout/Header.js", [], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/HistoricalPerformanceChart.js", [], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/SurpriseFactorChart.js", [], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/MarketImpactChart.js", [], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/pages/TestPage.js", [], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/HistoricalTrendsChart.js", [], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/pages/ScenarioAnalysis.js", ["145", "146"], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/ScenarioChart.js", ["147"], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/pages/TradingStrategy.js", [], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/pages/DailyFundamentals.js", [], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/layout/LoadingSpinner.js", [], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/layout/ErrorAlert.js", [], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/ESFuturesChart.js", [], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/VIXFuturesChart.js", [], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/services/api.js", ["148"], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/ChartSettings.js", [], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/TradingViewChartWrapper.js", [], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/TradingViewChart.js", ["149", "150"], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/PriceLevelsManager.js", [], [], "/Users/<USER>/Desktop/AI/vibe/MacroAnalysis/app/frontend/src/components/charts/SavedChartGuide.js", [], [], {"ruleId": "151", "severity": 1, "message": "152", "line": 68, "column": 6, "nodeType": "153", "endLine": 68, "endColumn": 8, "suggestions": "154"}, {"ruleId": "151", "severity": 1, "message": "152", "line": 76, "column": 6, "nodeType": "153", "endLine": 76, "endColumn": 65, "suggestions": "155", "suppressions": "156"}, {"ruleId": "151", "severity": 1, "message": "157", "line": 75, "column": 6, "nodeType": "153", "endLine": 75, "endColumn": 8, "suggestions": "158"}, {"ruleId": "159", "severity": 1, "message": "160", "line": 2, "column": 8, "nodeType": "161", "messageId": "162", "endLine": 2, "endColumn": 21}, {"ruleId": "151", "severity": 1, "message": "163", "line": 331, "column": 6, "nodeType": "153", "endLine": 331, "endColumn": 21, "suggestions": "164"}, {"ruleId": "151", "severity": 1, "message": "165", "line": 16, "column": 6, "nodeType": "153", "endLine": 16, "endColumn": 32, "suggestions": "166"}, {"ruleId": "167", "severity": 1, "message": "168", "line": 76, "column": 1, "nodeType": "169", "endLine": 81, "endColumn": 3}, {"ruleId": "159", "severity": 1, "message": "170", "line": 1, "column": 36, "nodeType": "161", "messageId": "162", "endLine": 1, "endColumn": 44}, {"ruleId": "151", "severity": 1, "message": "171", "line": 144, "column": 6, "nodeType": "153", "endLine": 144, "endColumn": 45, "suggestions": "172"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchHistoricalTrends'. Either include it or remove the dependency array.", "ArrayExpression", ["173"], ["174"], ["175"], "React Hook useEffect has a missing dependency: 'selectedIndicator'. Either include it or remove the dependency array.", ["176"], "no-unused-vars", "'ScenarioChart' is defined but never used.", "Identifier", "unusedVar", "React Hook useEffect has a missing dependency: 'selectedInstrument'. Either include it or remove the dependency array.", ["177"], "React Hook useEffect has a missing dependency: 'renderScenarioChart'. Either include it or remove the dependency array.", ["178"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'useState' is defined but never used.", "React Hook useEffect has an unnecessary dependency: 'widgetRef.current'. Either exclude it or remove the dependency array. Mutable values like 'widgetRef.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["179"], {"desc": "180", "fix": "181"}, {"desc": "182", "fix": "183"}, {"kind": "184", "justification": "185"}, {"desc": "186", "fix": "187"}, {"desc": "188", "fix": "189"}, {"desc": "190", "fix": "191"}, {"desc": "192", "fix": "193"}, "Update the dependencies array to be: [fetchHistoricalTrends]", {"range": "194", "text": "195"}, "Update the dependencies array to be: [selectedIndicator, selectedPeriod, selectedYears, loading, fetchHistoricalTrends]", {"range": "196", "text": "197"}, "directive", "", "Update the dependencies array to be: [selectedIndicator]", {"range": "198", "text": "199"}, "Update the dependencies array to be: [selectedEvent, selectedInstrument]", {"range": "200", "text": "201"}, "Update the dependencies array to be: [scenarioData, instrument, renderScenarioChart]", {"range": "202", "text": "203"}, "Update the dependencies array to be: [pricelevels, theme]", {"range": "204", "text": "205"}, [2275, 2277], "[fetchHistoricalTrends]", [2492, 2551], "[selectedIndicator, selectedPeriod, selectedYears, loading, fetchHistoricalTrends]", [2550, 2552], "[selectedIndicator]", [15261, 15276], "[selected<PERSON><PERSON>, selectedInstrument]", [394, 420], "[scenarioData, instrument, renderScenarioChart]", [5033, 5072], "[pricelevels, theme]"]