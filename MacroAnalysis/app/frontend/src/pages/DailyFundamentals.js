import React, { useState, useEffect } from 'react';
import { futuresApi } from '../services/api';
import { Tabs, Tab } from 'react-bootstrap';
import ESFuturesChart from '../components/charts/ESFuturesChart';
import VIXFuturesChart from '../components/charts/VIXFuturesChart';
import TradingViewChartWrapper from '../components/charts/TradingViewChartWrapper';
import LoadingSpinner from '../components/layout/LoadingSpinner';
import ErrorAlert from '../components/layout/ErrorAlert';
import './DailyFundamentals.css';

const DailyFundamentals = () => {
  const [esData, setEsData] = useState([]);
  const [vixData, setVixData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [activeTab, setActiveTab] = useState('es');
  const [chartType, setChartType] = useState('custom'); // 'custom' or 'tradingview'
  const [chartSettings, setChartSettings] = useState({
    showVolume: false,
    showMovingAverages: false,
    maShortPeriod: 20,
    maLongPeriod: 50,
    showRSI: false,
    rsiPeriod: 14,
    theme: 'light'
  });

  const fetchFuturesData = async () => {
    setLoading(true);
    try {
      // Fetch ES futures data
      const esResponse = await futuresApi.getESFutures();
      setEsData(esResponse.data.data);
      
      // Fetch VIX futures data
      const vixResponse = await futuresApi.getVIXFutures();
      setVixData(vixResponse.data.data);
      
      // Set last updated timestamp
      if (esResponse.data.last_updated) {
        setLastUpdated(new Date(esResponse.data.last_updated * 1000).toLocaleString());
      }
      
      setError(null);
    } catch (err) {
      console.error('Error fetching futures data:', err);
      setError('Failed to load futures data. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleRefreshData = async () => {
    setRefreshing(true);
    try {
      await futuresApi.refreshFuturesData();
      await fetchFuturesData();
    } catch (err) {
      console.error('Error refreshing futures data:', err);
      setError('Failed to refresh futures data. Please try again later.');
    } finally {
      setRefreshing(false);
    }
  };
  
  useEffect(() => {
    fetchFuturesData();
  }, []);

  const handleTabChange = (tab) => {
    setActiveTab(tab);
  };
  
  const handleChartSettingsChange = (newSettings) => {
    setChartSettings(newSettings);
  };
  
  const handleChartTypeChange = (type) => {
    setChartType(type);
  };

  if (loading) {
    return <LoadingSpinner message="Loading futures data..." />;
  }

  if (error) {
    return <ErrorAlert message={error} />;
  }

  return (
    <div className="daily-fundamentals-container">
      <h1>Daily Fundamentals</h1>
      <div className="page-header-container">
        <p className="description">
          Track daily ES futures and VIX futures to monitor market sentiment and volatility trends.
        </p>
        <div className="actions-container">
          {lastUpdated && (
            <span className="last-updated">
              Last updated: {lastUpdated}
            </span>
          )}
          <div className="chart-type-toggle">
            <button 
              className={chartType === 'custom' ? 'active' : ''}
              onClick={() => handleChartTypeChange('custom')}
            >
              Custom Charts
            </button>
            <button 
              className={chartType === 'tradingview' ? 'active' : ''}
              onClick={() => handleChartTypeChange('tradingview')}
            >
              TradingView
            </button>
          </div>
          <button 
            className={`refresh-button ${refreshing ? 'refreshing' : ''}`}
            onClick={handleRefreshData}
            disabled={refreshing || loading}
          >
            {refreshing ? 'Refreshing...' : 'Refresh Data'}
          </button>
        </div>
      </div>
      
      <div className="data-summary">
        <div className="summary-card">
          <h3>ES Futures</h3>
          <p>E-mini S&P 500 futures represent market expectations for the S&P 500 index.</p>
          {esData.length > 0 && (
            <div className="stats">
              <div className="stat-item">
                <span className="label">Latest Close:</span>
                <span className="value">{parseFloat(esData[esData.length - 1].close).toFixed(2)}</span>
              </div>
              <div className="stat-item">
                <span className="label">Data Points:</span>
                <span className="value">{esData.length}</span>
              </div>
            </div>
          )}
        </div>
        
        <div className="summary-card">
          <h3>VIX Futures</h3>
          <p>VIX futures track expected market volatility over the coming months.</p>
          {vixData.length > 0 && (
            <div className="stats">
              <div className="stat-item">
                <span className="label">Latest Close:</span>
                <span className="value">{parseFloat(vixData[vixData.length - 1].close).toFixed(2)}</span>
              </div>
              <div className="stat-item">
                <span className="label">Data Points:</span>
                <span className="value">{vixData.length}</span>
              </div>
            </div>
          )}
        </div>
      </div>
      
      <div className="chart-container">
        {chartType === 'custom' ? (
          <Tabs
            activeKey={activeTab}
            onSelect={handleTabChange}
            id="futures-tabs"
            className="mb-3 futures-tabs"
          >
            <Tab eventKey="es" title="ES Futures">
              {esData.length > 0 ? (
                <ESFuturesChart 
                  data={esData} 
                  initialSettings={chartSettings}
                  onSettingsChange={handleChartSettingsChange}
                />
              ) : (
                <p>No ES futures data available</p>
              )}
            </Tab>
            <Tab eventKey="vix" title="VIX Futures">
              {vixData.length > 0 ? (
                <VIXFuturesChart 
                  data={vixData} 
                  initialSettings={chartSettings}
                  onSettingsChange={handleChartSettingsChange}
                />
              ) : (
                <p>No VIX futures data available</p>
              )}
            </Tab>
          </Tabs>
        ) : (
          <TradingViewChartWrapper 
            symbol={activeTab === 'es' ? 'ES' : 'VIX'}
            initialSettings={chartSettings}
            onSettingsChange={handleChartSettingsChange}
          />
        )}
      </div>
      
      <div className="data-insights">
        <h2>Market Insights</h2>
        <div className="insight-cards">
          <div className="insight-card">
            <h3>ES Futures Analysis</h3>
            <p>
              {esData.length > 0 
                ? `The ES futures have ${
                    esData[esData.length - 1].close > esData[esData.length - 2].close 
                      ? 'risen' 
                      : 'fallen'
                  } recently, indicating ${
                    esData[esData.length - 1].close > esData[esData.length - 2].close 
                      ? 'positive' 
                      : 'negative'
                  } market sentiment.`
                : 'Insufficient data for analysis.'}
            </p>
          </div>
          <div className="insight-card">
            <h3>VIX Futures Analysis</h3>
            <p>
              {vixData.length > 0 
                ? `The VIX futures are currently ${
                    vixData[vixData.length - 1].close > 20 
                      ? 'above 20, suggesting elevated market uncertainty' 
                      : 'below 20, suggesting relatively low expected volatility'
                  }.`
                : 'Insufficient data for analysis.'}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DailyFundamentals;