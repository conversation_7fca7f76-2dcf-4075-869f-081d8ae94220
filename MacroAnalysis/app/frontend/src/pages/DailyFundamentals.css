.daily-fundamentals-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.daily-fundamentals-container h1 {
  margin-bottom: 1rem;
  color: #333;
  font-size: 2rem;
}

.page-header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.description {
  font-size: 1.1rem;
  color: #555;
  margin: 0;
  flex: 1;
}

.actions-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.last-updated {
  color: #666;
  font-size: 0.9rem;
}

.chart-type-toggle {
  display: flex;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.dark .chart-type-toggle {
  border-color: #444;
}

.blue .chart-type-toggle {
  border-color: #c9e0f9;
}

.chart-type-toggle button {
  background-color: #f5f5f5;
  border: none;
  color: #555;
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark .chart-type-toggle button {
  background-color: #333;
  color: #aaa;
}

.blue .chart-type-toggle button {
  background-color: #e6f0fb;
  color: #0066cc;
}

.chart-type-toggle button:first-child {
  border-right: 1px solid #ddd;
}

.dark .chart-type-toggle button:first-child {
  border-right-color: #444;
}

.blue .chart-type-toggle button:first-child {
  border-right-color: #c9e0f9;
}

.chart-type-toggle button.active {
  background-color: #2c6ecf;
  color: white;
}

.dark .chart-type-toggle button.active {
  background-color: #0066cc;
  color: white;
}

.blue .chart-type-toggle button.active {
  background-color: #0066cc;
  color: white;
}

.refresh-button {
  background-color: #2c6ecf;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
}

.refresh-button:hover {
  background-color: #205bb0;
}

.refresh-button:disabled {
  background-color: #6c94c7;
  cursor: not-allowed;
}

.refresh-button.refreshing {
  position: relative;
  overflow: hidden;
}

.refresh-button.refreshing::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: refresh-loading 1.5s infinite;
}

@keyframes refresh-loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.data-summary {
  display: flex;
  justify-content: space-between;
  gap: 2rem;
  margin-bottom: 2rem;
}

.summary-card {
  flex: 1;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.summary-card h3 {
  margin-bottom: 0.75rem;
  color: #333;
  font-size: 1.3rem;
}

.summary-card p {
  color: #666;
  margin-bottom: 1rem;
}

.stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #eee;
}

.stat-item .label {
  font-weight: 500;
  color: #555;
}

.stat-item .value {
  font-weight: 600;
  color: #333;
}

.chart-container {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  margin-bottom: 2rem;
}

.futures-tabs .nav-link {
  color: #555;
  font-weight: 500;
}

.futures-tabs .nav-link.active {
  color: #2c6ecf;
  font-weight: 600;
}

.data-insights {
  margin-top: 2rem;
}

.data-insights h2 {
  margin-bottom: 1rem;
  font-size: 1.5rem;
  color: #333;
}

.insight-cards {
  display: flex;
  gap: 2rem;
}

.insight-card {
  flex: 1;
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.insight-card h3 {
  margin-bottom: 0.75rem;
  color: #333;
  font-size: 1.3rem;
}

.insight-card p {
  color: #666;
}

@media (max-width: 768px) {
  .data-summary,
  .insight-cards {
    flex-direction: column;
    gap: 1rem;
  }
  
  .daily-fundamentals-container {
    padding: 1rem;
  }
}