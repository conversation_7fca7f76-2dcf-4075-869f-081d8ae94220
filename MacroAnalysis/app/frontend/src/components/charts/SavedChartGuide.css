.saved-chart-guide {
  position: relative;
  display: inline-block;
  margin-top: 0.5rem;
}

.help-button {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  color: #555;
  padding: 0.4rem 0.75rem;
  font-size: 0.85rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.help-button:hover {
  background-color: #e0e0e0;
}

.dark .help-button {
  background-color: #333;
  border-color: #444;
  color: #aaa;
}

.dark .help-button:hover {
  background-color: #444;
}

.blue .help-button {
  background-color: #e6f0fb;
  border-color: #c9e0f9;
  color: #0066cc;
}

.blue .help-button:hover {
  background-color: #d1e6ff;
}

.guide-content {
  position: absolute;
  top: calc(100% + 10px);
  right: 0;
  width: 400px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
  padding: 1.5rem;
  z-index: 1000;
  max-height: 80vh;
  overflow-y: auto;
}

.dark .guide-content {
  background-color: #1e1e1e;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
  color: #f0f0f0;
}

.blue .guide-content {
  background-color: #f5f9ff;
  box-shadow: 0 5px 20px rgba(0, 40, 100, 0.15);
}

.guide-content h3 {
  margin-top: 0;
  margin-bottom: 1.25rem;
  font-size: 1.1rem;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.75rem;
}

.dark .guide-content h3 {
  color: #f0f0f0;
  border-bottom: 1px solid #333;
}

.blue .guide-content h3 {
  color: #0066cc;
  border-bottom: 1px solid #c9e0f9;
}

.guide-steps {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  margin-bottom: 1.5rem;
}

.step {
  display: flex;
  gap: 1rem;
}

.step-number {
  background-color: #2c6ecf;
  color: white;
  font-weight: 600;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.dark .step-number {
  background-color: #0066cc;
}

.blue .step-number {
  background-color: #0066cc;
}

.step-content {
  flex: 1;
}

.step-content h4 {
  margin: 0 0 0.5rem 0;
  font-size: 0.95rem;
  color: #333;
}

.dark .step-content h4 {
  color: #f0f0f0;
}

.blue .step-content h4 {
  color: #0066cc;
}

.step-content p {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: #555;
  line-height: 1.4;
}

.dark .step-content p {
  color: #ccc;
}

.blue .step-content p {
  color: #333;
}

.step-content code {
  background: #f5f5f5;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: monospace;
  font-size: 0.85rem;
  color: #333;
  border: 1px solid #eee;
  word-break: break-all;
}

.dark .step-content code {
  background: #2a2a2a;
  color: #f0f0f0;
  border-color: #444;
}

.blue .step-content code {
  background: #e6f0fb;
  color: #0066cc;
  border-color: #c9e0f9;
}

.guide-notes {
  background: #f9f9f9;
  padding: 1rem;
  border-radius: 6px;
  margin-top: 1rem;
}

.dark .guide-notes {
  background: #2a2a2a;
}

.blue .guide-notes {
  background: #e6f0fb;
}

.guide-notes h4 {
  margin: 0 0 0.75rem 0;
  font-size: 0.95rem;
  color: #333;
}

.dark .guide-notes h4 {
  color: #f0f0f0;
}

.blue .guide-notes h4 {
  color: #0066cc;
}

.guide-notes ul {
  margin: 0;
  padding-left: 1.25rem;
}

.guide-notes li {
  margin-bottom: 0.5rem;
  font-size: 0.85rem;
  color: #555;
  line-height: 1.4;
}

.guide-notes li:last-child {
  margin-bottom: 0;
}

.dark .guide-notes li {
  color: #ccc;
}

.blue .guide-notes li {
  color: #333;
}

@media (max-width: 500px) {
  .guide-content {
    width: 300px;
    right: -100px;
  }
}