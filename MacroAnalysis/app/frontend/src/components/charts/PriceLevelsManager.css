.price-levels-manager {
  background-color: white;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.dark .price-levels-manager {
  background-color: #1e1e1e;
  color: #f0f0f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.blue .price-levels-manager {
  background-color: #ebf5ff;
  box-shadow: 0 2px 8px rgba(0, 40, 100, 0.1);
}

.price-levels-manager h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.2rem;
  color: #333;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.5rem;
}

.dark .price-levels-manager h3 {
  color: #f0f0f0;
  border-bottom: 1px solid #333;
}

.blue .price-levels-manager h3 {
  color: #0066cc;
  border-bottom: 1px solid #c9e0f9;
}

.price-levels-list {
  margin-bottom: 1rem;
  max-height: 200px;
  overflow-y: auto;
}

.price-level-item {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  border-bottom: 1px solid #f5f5f5;
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.dark .price-level-item {
  border-bottom: 1px solid #333;
}

.blue .price-level-item {
  border-bottom: 1px solid #e6f0fb;
}

.price-level-item:hover {
  background-color: #f9f9f9;
}

.dark .price-level-item:hover {
  background-color: #2a2a2a;
}

.blue .price-level-item:hover {
  background-color: #e6f0fb;
}

.price-level-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 0.75rem;
}

.price-level-info {
  display: flex;
  justify-content: space-between;
  flex: 1;
  margin-right: 0.75rem;
}

.price-level-label {
  font-weight: 500;
  color: #555;
}

.dark .price-level-label {
  color: #ccc;
}

.blue .price-level-label {
  color: #333;
}

.price-level-value {
  font-weight: 600;
  color: #333;
}

.dark .price-level-value {
  color: #eee;
}

.remove-level-button {
  background: transparent;
  border: none;
  color: #999;
  font-size: 0.9rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.remove-level-button:hover {
  background-color: #f0f0f0;
  color: #f44336;
}

.dark .remove-level-button:hover {
  background-color: #333;
  color: #ff5252;
}

.blue .remove-level-button:hover {
  background-color: #daeeff;
  color: #d32f2f;
}

.add-level-button {
  width: 100%;
  background-color: #f5f5f5;
  border: 1px dashed #ccc;
  color: #555;
  padding: 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.add-level-button:hover {
  background-color: #e9e9e9;
  color: #333;
}

.dark .add-level-button {
  background-color: #2a2a2a;
  border: 1px dashed #555;
  color: #ccc;
}

.dark .add-level-button:hover {
  background-color: #333;
  color: #eee;
}

.blue .add-level-button {
  background-color: #e6f0fb;
  border: 1px dashed #c9e0f9;
  color: #0066cc;
}

.blue .add-level-button:hover {
  background-color: #daeeff;
  color: #004b99;
}

.no-levels-message {
  color: #888;
  font-style: italic;
  text-align: center;
  padding: 1rem 0;
}

.dark .no-levels-message {
  color: #777;
}

.add-level-form {
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 1rem;
  margin-top: 0.5rem;
}

.dark .add-level-form {
  background-color: #2a2a2a;
}

.blue .add-level-form {
  background-color: #e6f0fb;
}

.form-row {
  display: flex;
  margin-bottom: 0.75rem;
  align-items: center;
}

.form-row label {
  width: 80px;
  color: #555;
  font-size: 0.9rem;
}

.dark .form-row label {
  color: #aaa;
}

.blue .form-row label {
  color: #0066cc;
}

.form-row input,
.form-row select {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
}

.dark .form-row input,
.dark .form-row select {
  border: 1px solid #444;
  background-color: #333;
  color: #eee;
}

.blue .form-row input,
.blue .form-row select {
  border: 1px solid #c9e0f9;
  background-color: white;
}

.form-row input[type="color"] {
  height: 36px;
  padding: 2px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 1rem;
}

.cancel-button,
.add-button {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button {
  background-color: transparent;
  border: 1px solid #ddd;
  color: #555;
}

.cancel-button:hover {
  background-color: #f0f0f0;
}

.dark .cancel-button {
  border: 1px solid #444;
  color: #aaa;
}

.dark .cancel-button:hover {
  background-color: #333;
}

.blue .cancel-button {
  border: 1px solid #c9e0f9;
  color: #0066cc;
}

.blue .cancel-button:hover {
  background-color: #daeeff;
}

.add-button {
  background-color: #2c6ecf;
  border: 1px solid #2c6ecf;
  color: white;
}

.add-button:hover {
  background-color: #205bb0;
}

.add-button:disabled {
  background-color: #a0b8e0;
  border-color: #a0b8e0;
  cursor: not-allowed;
}

.dark .add-button:disabled {
  background-color: #444;
  border-color: #444;
}