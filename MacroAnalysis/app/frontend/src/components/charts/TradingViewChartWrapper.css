.tradingview-chart-wrapper {
  width: 100%;
  background-color: #ffffff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
  padding: 1rem;
  transition: all 0.3s ease;
}

.tradingview-chart-wrapper.dark {
  background-color: #1e1e1e;
  color: #f0f0f0;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
}

.tradingview-chart-wrapper.blue {
  background-color: #ebf5ff;
  box-shadow: 0 2px 8px rgba(0, 40, 100, 0.1);
}

.chart-controls-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding: 0 0.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.symbol-selector,
.interval-selector,
.saved-chart-inputs {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.symbol-selector label,
.interval-selector label,
.input-group label {
  font-weight: 500;
  color: #555;
  white-space: nowrap;
}

.dark .symbol-selector label,
.dark .interval-selector label,
.dark .input-group label {
  color: #ccc;
}

.blue .symbol-selector label,
.blue .interval-selector label,
.blue .input-group label {
  color: #0066cc;
}

.symbol-selector select,
.interval-selector select {
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  border: 1px solid #ddd;
  background-color: white;
  font-size: 0.9rem;
}

.dark .symbol-selector select,
.dark .interval-selector select {
  background-color: #333;
  border-color: #444;
  color: #eee;
}

.blue .symbol-selector select,
.blue .interval-selector select {
  border-color: #c9e0f9;
  color: #333;
}

.saved-chart-inputs {
  display: flex;
  gap: 1rem;
  flex: 1;
  flex-wrap: wrap;
}

.input-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.input-group input {
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  border: 1px solid #ddd;
  background-color: white;
  font-size: 0.9rem;
  min-width: 200px;
}

.dark .input-group input {
  background-color: #333;
  border-color: #444;
  color: #eee;
}

.blue .input-group input {
  border-color: #c9e0f9;
  color: #333;
}

.chart-mode-toggle {
  margin-left: auto;
}

.chart-mode-toggle button {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  color: #555;
  padding: 0.5rem 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark .chart-mode-toggle button {
  background-color: #333;
  border-color: #444;
  color: #aaa;
}

.blue .chart-mode-toggle button {
  background-color: #e6f0fb;
  border-color: #c9e0f9;
  color: #0066cc;
}

.chart-mode-toggle button:hover {
  background-color: #e0e0e0;
}

.dark .chart-mode-toggle button:hover {
  background-color: #444;
}

.blue .chart-mode-toggle button:hover {
  background-color: #d1e6ff;
}

.chart-mode-toggle button.active {
  background-color: #2c6ecf;
  border-color: #2c6ecf;
  color: white;
}

.chart-content-row {
  display: flex;
  gap: 1.5rem;
  margin-top: 1rem;
}

.tradingview-chart-column {
  flex: 3;
}

.price-levels-column {
  flex: 1;
  min-width: 250px;
}

@media (max-width: 1200px) {
  .chart-content-row {
    flex-direction: column;
  }
  
  .price-levels-column {
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .chart-controls-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .symbol-selector,
  .interval-selector,
  .saved-chart-inputs,
  .chart-mode-toggle {
    width: 100%;
  }
  
  .chart-mode-toggle {
    margin-left: 0;
    margin-top: 0.5rem;
  }
  
  .chart-mode-toggle button {
    width: 100%;
  }
  
  .saved-chart-inputs {
    flex-direction: column;
  }
  
  .input-group {
    width: 100%;
  }
  
  .input-group input {
    width: 100%;
    min-width: auto;
  }
}