import React, { useState } from 'react';
import './PriceLevelsManager.css';

const PriceLevelsManager = ({ priceLevels, onAddLevel, onRemoveLevel, onEditLevel }) => {
  const [isAdding, setIsAdding] = useState(false);
  const [newLevel, setNewLevel] = useState({
    price: '',
    type: 'support', // 'support' or 'resistance'
    label: '',
    color: '',
    width: 2,
    style: 1 // 0-solid, 1-dotted, 2-dashed
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewLevel(prev => ({
      ...prev,
      [name]: name === 'price' || name === 'width' ? parseFloat(value) : value
    }));
  };

  const handleAddLevel = () => {
    if (!newLevel.price) return;
    
    // Set default color based on type if not specified
    const levelToAdd = {
      ...newLevel,
      color: newLevel.color || (newLevel.type === 'support' ? '#4CAF50' : '#F44336'),
      id: Date.now() // Add unique id
    };
    
    onAddLevel(levelToAdd);
    
    // Reset form
    setNewLevel({
      price: '',
      type: 'support',
      label: '',
      color: '',
      width: 2,
      style: 1
    });
    setIsAdding(false);
  };

  const handleCancelAdd = () => {
    setIsAdding(false);
    setNewLevel({
      price: '',
      type: 'support',
      label: '',
      color: '',
      width: 2,
      style: 1
    });
  };

  return (
    <div className="price-levels-manager">
      <h3>Price Levels</h3>
      
      {priceLevels.length > 0 ? (
        <div className="price-levels-list">
          {priceLevels.map(level => (
            <div key={level.id} className="price-level-item">
              <div 
                className="price-level-color" 
                style={{ backgroundColor: level.color }}
              ></div>
              <div className="price-level-info">
                <span className="price-level-label">
                  {level.label || (level.type === 'support' ? 'Support' : 'Resistance')}
                </span>
                <span className="price-level-value">{level.price.toFixed(2)}</span>
              </div>
              <button 
                className="remove-level-button"
                onClick={() => onRemoveLevel(level.id)}
                title="Remove level"
              >
                ✕
              </button>
            </div>
          ))}
        </div>
      ) : (
        <p className="no-levels-message">No price levels added yet</p>
      )}
      
      {isAdding ? (
        <div className="add-level-form">
          <div className="form-row">
            <label htmlFor="price">Price:</label>
            <input 
              type="number" 
              id="price" 
              name="price" 
              value={newLevel.price} 
              onChange={handleInputChange}
              step="0.01"
              required
            />
          </div>
          
          <div className="form-row">
            <label htmlFor="type">Type:</label>
            <select 
              id="type" 
              name="type" 
              value={newLevel.type} 
              onChange={handleInputChange}
            >
              <option value="support">Support</option>
              <option value="resistance">Resistance</option>
            </select>
          </div>
          
          <div className="form-row">
            <label htmlFor="label">Label:</label>
            <input 
              type="text" 
              id="label" 
              name="label" 
              value={newLevel.label} 
              onChange={handleInputChange}
              placeholder={newLevel.type === 'support' ? 'Support' : 'Resistance'}
            />
          </div>
          
          <div className="form-row">
            <label htmlFor="color">Color:</label>
            <input 
              type="color" 
              id="color" 
              name="color" 
              value={newLevel.color || (newLevel.type === 'support' ? '#4CAF50' : '#F44336')} 
              onChange={handleInputChange}
            />
          </div>
          
          <div className="form-row">
            <label htmlFor="width">Width:</label>
            <input 
              type="number" 
              id="width" 
              name="width" 
              value={newLevel.width} 
              onChange={handleInputChange}
              min="1"
              max="4"
            />
          </div>
          
          <div className="form-row">
            <label htmlFor="style">Style:</label>
            <select 
              id="style" 
              name="style" 
              value={newLevel.style} 
              onChange={handleInputChange}
            >
              <option value={0}>Solid</option>
              <option value={1}>Dotted</option>
              <option value={2}>Dashed</option>
            </select>
          </div>
          
          <div className="form-actions">
            <button 
              className="cancel-button" 
              onClick={handleCancelAdd}
            >
              Cancel
            </button>
            <button 
              className="add-button" 
              onClick={handleAddLevel}
              disabled={!newLevel.price}
            >
              Add Level
            </button>
          </div>
        </div>
      ) : (
        <button 
          className="add-level-button"
          onClick={() => setIsAdding(true)}
        >
          + Add Price Level
        </button>
      )}
    </div>
  );
};

export default PriceLevelsManager;