import React, { useState, useEffect } from 'react';
import TradingViewChart from './TradingViewChart';
import PriceLevelsManager from './PriceLevelsManager';
import SavedChartGuide from './SavedChartGuide';
import './TradingViewChartWrapper.css';

const TradingViewChartWrapper = ({ symbol, initialSettings, onSettingsChange }) => {
  const [chartSettings, setChartSettings] = useState({
    symbol: symbol || 'ES',
    interval: 'D',
    theme: initialSettings?.theme || 'light',
    useSavedChart: false,
    savedChartId: '',
    username: ''
  });
  
  const [priceLevels, setPriceLevels] = useState([]);
  
  // Update the chart theme when the global theme changes
  useEffect(() => {
    if (initialSettings?.theme) {
      setChartSettings(prev => ({
        ...prev,
        theme: initialSettings.theme
      }));
    }
  }, [initialSettings?.theme]);
  
  const handleAddPriceLevel = (level) => {
    setPriceLevels(prev => [...prev, level]);
  };
  
  const handleRemovePriceLevel = (id) => {
    setPriceLevels(prev => prev.filter(level => level.id !== id));
  };
  
  const handleEditPriceLevel = (id, updatedLevel) => {
    setPriceLevels(prev => 
      prev.map(level => level.id === id ? { ...level, ...updatedLevel } : level)
    );
  };
  
  const handleSymbolChange = (e) => {
    const newSymbol = e.target.value;
    setChartSettings(prev => ({
      ...prev,
      symbol: newSymbol
    }));
  };
  
  const handleIntervalChange = (e) => {
    const newInterval = e.target.value;
    setChartSettings(prev => ({
      ...prev,
      interval: newInterval
    }));
  };
  
  const handleSavedChartToggle = () => {
    setChartSettings(prev => ({
      ...prev,
      useSavedChart: !prev.useSavedChart
    }));
  };
  
  const handleSavedChartIdChange = (e) => {
    setChartSettings(prev => ({
      ...prev,
      savedChartId: e.target.value
    }));
  };
  
  const handleUsernameChange = (e) => {
    setChartSettings(prev => ({
      ...prev,
      username: e.target.value
    }));
  };
  
  return (
    <div className={`tradingview-chart-wrapper ${chartSettings.theme}`}>
      <div className="chart-controls-row">
        {!chartSettings.useSavedChart ? (
          <>
            <div className="symbol-selector">
              <label htmlFor="symbol-select">Symbol:</label>
              <select 
                id="symbol-select" 
                value={chartSettings.symbol} 
                onChange={handleSymbolChange}
              >
                <option value="ES">ES (E-mini S&P 500)</option>
                <option value="NQ">NQ (E-mini NASDAQ)</option>
                <option value="YM">YM (E-mini Dow)</option>
                <option value="RTY">RTY (E-mini Russell 2000)</option>
                <option value="VIX">VIX (Volatility Index)</option>
                <option value="VX">VX (VIX Futures)</option>
                <option value="GC">GC (Gold Futures)</option>
                <option value="SI">SI (Silver Futures)</option>
                <option value="CL">CL (Crude Oil Futures)</option>
                <option value="NG">NG (Natural Gas Futures)</option>
                <option value="ZB">ZB (30Y Treasury Bond Futures)</option>
                <option value="ZN">ZN (10Y Treasury Note Futures)</option>
                <option value="ZF">ZF (5Y Treasury Note Futures)</option>
                <option value="ZT">ZT (2Y Treasury Note Futures)</option>
              </select>
            </div>
            
            <div className="interval-selector">
              <label htmlFor="interval-select">Timeframe:</label>
              <select 
                id="interval-select" 
                value={chartSettings.interval} 
                onChange={handleIntervalChange}
              >
                <option value="1">1 Minute</option>
                <option value="5">5 Minutes</option>
                <option value="15">15 Minutes</option>
                <option value="30">30 Minutes</option>
                <option value="60">1 Hour</option>
                <option value="240">4 Hours</option>
                <option value="D">Daily</option>
                <option value="W">Weekly</option>
                <option value="M">Monthly</option>
              </select>
            </div>
          </>
        ) : (
          <>
            <div className="saved-chart-inputs">
              <div className="input-group">
                <label htmlFor="username">Username:</label>
                <input
                  id="username"
                  type="text"
                  value={chartSettings.username}
                  onChange={handleUsernameChange}
                  placeholder="Your TradingView username"
                />
              </div>
              <div className="input-group">
                <label htmlFor="chartId">Chart ID:</label>
                <input
                  id="chartId"
                  type="text"
                  value={chartSettings.savedChartId}
                  onChange={handleSavedChartIdChange}
                  placeholder="Your saved chart ID"
                />
              </div>
              <SavedChartGuide />
            </div>
          </>
        )}
        
        <div className="chart-mode-toggle">
          <button 
            className={chartSettings.useSavedChart ? 'active' : ''}
            onClick={handleSavedChartToggle}
            title={chartSettings.useSavedChart ? 'Switch to symbol mode' : 'Use your saved chart'}
          >
            {chartSettings.useSavedChart ? 'Using Saved Chart' : 'Use Saved Chart'}
          </button>
        </div>
      </div>
      
      <div className="chart-content-row">
        <div className="tradingview-chart-column">
          <TradingViewChart 
            symbol={chartSettings.symbol}
            interval={chartSettings.interval}
            theme={chartSettings.theme}
            pricelevels={priceLevels}
            savedChartId={chartSettings.useSavedChart ? chartSettings.savedChartId : null}
            username={chartSettings.useSavedChart ? chartSettings.username : null}
          />
        </div>
        
        <div className="price-levels-column">
          <PriceLevelsManager 
            priceLevels={priceLevels}
            onAddLevel={handleAddPriceLevel}
            onRemoveLevel={handleRemovePriceLevel}
            onEditLevel={handleEditPriceLevel}
          />
        </div>
      </div>
    </div>
  );
};

export default TradingViewChartWrapper;