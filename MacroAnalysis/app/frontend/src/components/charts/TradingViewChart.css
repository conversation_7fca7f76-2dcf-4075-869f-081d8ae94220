.tradingview-chart-container {
  width: 100%;
  height: 600px;
  position: relative;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.tradingview-chart {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

/* Dark theme support */
.dark .tradingview-chart-container {
  background-color: #1e1e1e;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
}

/* Blue theme support */
.blue .tradingview-chart-container {
  background-color: #ebf5ff;
  box-shadow: 0 2px 8px rgba(0, 40, 100, 0.1);
}

/* Loading state */
.tradingview-chart-container::before {
  content: 'Loading chart...';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #888;
  font-size: 16px;
  z-index: 0;
}

/* Price levels info panel */
.price-levels-panel {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 100;
  max-width: 250px;
  font-size: 12px;
}

.dark .price-levels-panel {
  background-color: rgba(30, 30, 30, 0.9);
  color: #eee;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.blue .price-levels-panel {
  background-color: rgba(235, 245, 255, 0.9);
  box-shadow: 0 2px 4px rgba(0, 40, 100, 0.1);
}

.price-level-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.price-level-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 8px;
  display: inline-block;
}

.price-level-info {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.price-level-label {
  font-weight: 500;
}

.price-level-value {
  font-weight: 600;
}