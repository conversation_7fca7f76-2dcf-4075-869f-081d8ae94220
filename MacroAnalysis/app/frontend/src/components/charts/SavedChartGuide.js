import React, { useState } from 'react';
import './SavedChartGuide.css';

const SavedChartGuide = () => {
  const [showGuide, setShowGuide] = useState(false);
  
  return (
    <div className="saved-chart-guide">
      <button 
        className="help-button"
        onClick={() => setShowGuide(!showGuide)}
        title={showGuide ? "Hide guide" : "How to find your saved chart ID"}
      >
        {showGuide ? "Hide Guide" : "Help"}
      </button>
      
      {showGuide && (
        <div className="guide-content">
          <h3>How to Find Your TradingView Saved Chart ID</h3>
          
          <div className="guide-steps">
            <div className="step">
              <div className="step-number">1</div>
              <div className="step-content">
                <h4>Log in to TradingView</h4>
                <p>Make sure you're logged into your TradingView account at <a href="https://www.tradingview.com/" target="_blank" rel="noopener noreferrer">tradingview.com</a>.</p>
              </div>
            </div>
            
            <div className="step">
              <div className="step-number">2</div>
              <div className="step-content">
                <h4>Open the chart you want to use</h4>
                <p>Navigate to the saved chart you want to display in this application.</p>
              </div>
            </div>
            
            <div className="step">
              <div className="step-number">3</div>
              <div className="step-content">
                <h4>Find the chart ID in the URL</h4>
                <p>In your browser's address bar, you'll see a URL like:</p>
                <code>https://www.tradingview.com/chart/XXXXXXXXXXXX/</code>
                <p>The part after "/chart/" and before the next slash is your chart ID.</p>
              </div>
            </div>
            
            <div className="step">
              <div className="step-number">4</div>
              <div className="step-content">
                <h4>Copy your chart ID</h4>
                <p>Copy the alphanumeric string (usually 12 characters) that appears after "/chart/" in the URL.</p>
              </div>
            </div>
            
            <div className="step">
              <div className="step-number">5</div>
              <div className="step-content">
                <h4>Enter your username and chart ID</h4>
                <p>Paste the chart ID into the "Chart ID" field and enter your TradingView username in the "Username" field.</p>
              </div>
            </div>
          </div>
          
          <div className="guide-notes">
            <h4>Important Notes:</h4>
            <ul>
              <li>Your chart must be saved to your TradingView account to be accessible.</li>
              <li>Your TradingView username is not your email address. It's the name that appears in your profile URL: <code>tradingview.com/u/YOUR_USERNAME/</code></li>
              <li>Some advanced features may only be available with a TradingView subscription.</li>
              <li>Sharing a chart does not share your account credentials, only the chart configuration.</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default SavedChartGuide;