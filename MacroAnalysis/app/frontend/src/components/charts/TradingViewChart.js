import React, { useEffect, useRef, useState } from 'react';
import './TradingViewChart.css';

const TradingViewChart = ({ symbol = 'ES', interval = 'D', theme = 'light', pricelevels = [], savedChartId = null, username = null }) => {
  const containerRef = useRef(null);
  const scriptRef = useRef(null);
  const widgetRef = useRef(null);

  // Cleanup function to remove script and widget
  const cleanup = () => {
    if (scriptRef.current) {
      document.body.removeChild(scriptRef.current);
      scriptRef.current = null;
    }

    if (widgetRef.current) {
      widgetRef.current = null;
    }

    // Remove any TradingView related elements
    const tvElements = document.querySelectorAll('iframe[src*="tradingview.com"]');
    tvElements.forEach(el => el.remove());
  };

  useEffect(() => {
    // Only create widget if container is available
    if (!containerRef.current) return;

    // Clean up any existing scripts or widgets
    cleanup();

    // Create script element
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = 'https://s3.tradingview.com/tv.js';
    script.async = true;
    script.onload = () => {
      if (typeof window.TradingView !== 'undefined') {
        // Base configuration for widget
        const widgetOptions = {
          autosize: true,
          interval: interval,
          timezone: "America/New_York",
          theme: theme,
          style: "1",
          locale: "en",
          toolbar_bg: theme === 'dark' ? '#1e1e1e' : '#f1f3f6',
          enable_publishing: false,
          allow_symbol_change: true,
          container_id: 'tradingview_chart',
          hide_side_toolbar: false,
          studies: ['RSI@tv-basicstudies'],
          show_popup_button: true,
          popup_width: "1000",
          popup_height: "650",
          save_image: true,
          studies_overrides: {},
          overrides: {
            "mainSeriesProperties.style": 1,
            "paneProperties.background": theme === 'dark' ? '#1e1e1e' : '#ffffff',
            "paneProperties.vertGridProperties.color": theme === 'dark' ? '#333333' : '#e0e0e0',
            "paneProperties.horzGridProperties.color": theme === 'dark' ? '#333333' : '#e0e0e0',
            "symbolWatermarkProperties.transparency": 90,
            "scalesProperties.textColor": theme === 'dark' ? '#aaa' : '#333',
          },
          loading_screen: { backgroundColor: theme === 'dark' ? '#1e1e1e' : '#ffffff' },
          disabled_features: [
            "header_symbol_search",
            "header_compare",
          ],
          enabled_features: [
            "use_localstorage_for_settings",
            "save_chart_properties_to_local_storage"
          ],
        };

        // If we have a saved chart ID and username, use that instead of the symbol
        if (savedChartId && username) {
          widgetOptions.chart = savedChartId;
          widgetOptions.username = username;
        } else {
          // Otherwise use the provided symbol
          widgetOptions.symbol = `${getSymbolPrefix(symbol)}:${symbol}`;
        }

        // Create the widget
        // @ts-ignore
        widgetRef.current = new window.TradingView.widget(widgetOptions);
      }
    };

    // Add script to document
    document.body.appendChild(script);
    scriptRef.current = script;

    // Cleanup on unmount
    return cleanup;
  }, [symbol, interval, theme, savedChartId, username]);

  // Draw price levels when the widget is loaded and ready
  useEffect(() => {
    if (!widgetRef.current || !pricelevels.length) return;

    const drawPriceLevels = () => {
      // Wait for the widget to be ready
      if (widgetRef.current && widgetRef.current.chart && widgetRef.current.chart()) {
        const chart = widgetRef.current.chart();

        // Clear existing lines
        chart.removeAllShapes();

        // Add each price level
        pricelevels.forEach(level => {
          chart.createMultipointShape(
            [
              { time: chart.getVisibleRange().from, price: level.price },
              { time: chart.getVisibleRange().to, price: level.price }
            ],
            {
              shape: 'horizontal_line',
              lock: true,
              disableSelection: true,
              disableSave: true,
              disableUndo: true,
              overrides: {
                linecolor: level.color || (level.type === 'support' ? '#4CAF50' : '#F44336'),
                linewidth: level.width || 2,
                linestyle: level.style || 1, // 0-solid, 1-dotted, 2-dashed
                showLabel: true,
                text: level.label || (level.type === 'support' ? 'Support' : 'Resistance'),
                textcolor: level.textColor || (theme === 'dark' ? '#fff' : '#333'),
              }
            }
          );
        });
      } else {
        // If not ready, try again in 500ms
        setTimeout(drawPriceLevels, 500);
      }
    };

    // Start the process
    setTimeout(drawPriceLevels, 1000);
  }, [pricelevels, widgetRef.current, theme]);

  // Helper function to get the correct symbol prefix based on the symbol
  const getSymbolPrefix = (symbol) => {
    const prefixes = {
      'ES': 'CME_MINI', // E-mini S&P 500
      'NQ': 'CME_MINI', // E-mini NASDAQ
      'YM': 'CME_MINI', // E-mini Dow
      'RTY': 'CME_MINI', // E-mini Russell 2000
      'VIX': 'CBOE',     // VIX index
      'VX': 'CFE',       // VIX futures
      'GC': 'COMEX',     // Gold futures
      'SI': 'COMEX',     // Silver futures
      'CL': 'NYMEX',     // Crude Oil futures
      'NG': 'NYMEX',     // Natural Gas futures
      'ZB': 'CBOT',      // 30-Year U.S. Treasury Bond futures
      'ZN': 'CBOT',      // 10-Year U.S. Treasury Note futures
      'ZF': 'CBOT',      // 5-Year U.S. Treasury Note futures
      'ZT': 'CBOT',      // 2-Year U.S. Treasury Note futures
      'default': 'CME'   // Default prefix
    };

    return prefixes[symbol] || prefixes['default'];
  };

  return (
    <div className="tradingview-chart-container">
      <div id="tradingview_chart" ref={containerRef} className="tradingview-chart"></div>
    </div>
  );
};

export default TradingViewChart;