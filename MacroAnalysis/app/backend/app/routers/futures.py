#!/usr/bin/env python3
"""
API endpoints for futures data.
"""

from fastapi import APIRouter, HTTPException
import pandas as pd
import os
import subprocess
import sys
from pathlib import Path
import logging
from typing import List, Dict, Any, Optional
from pydantic import BaseModel

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/futures", tags=["futures"])

class FuturesDataPoint(BaseModel):
    date: str
    open: float
    high: float
    low: float
    close: float
    volume: int

class FuturesDataResponse(BaseModel):
    data: List[Dict[str, Any]]
    count: int
    last_updated: float

class RefreshResponse(BaseModel):
    success: bool
    message: str
    last_updated: float

@router.get("/es", response_model=FuturesDataResponse)
async def get_es_futures():
    """API endpoint to get ES futures data."""
    try:
        # Path to the futures data
        data_dir = Path(__file__).resolve().parent.parent.parent.parent.parent / "data" / "market_data" / "futures"
        es_file_path = data_dir / "es_futures.csv"

        # Check if the file exists
        if not es_file_path.exists():
            logger.error(f"ES futures data file not found at {es_file_path}")
            raise HTTPException(
                status_code=404,
                detail="ES futures data not available. Please run the data fetching script first."
            )

        # Read the data
        es_data = pd.read_csv(es_file_path, skiprows=2)  # Skip the first two rows (headers)

        # Convert to JSON format suitable for charts
        chart_data = []
        for _, row in es_data.iterrows():
            try:
                chart_data.append({
                    "date": row[0],  # Date is in the first column
                    "open": float(row[4]),  # Open is in the 5th column
                    "high": float(row[2]),  # High is in the 3rd column
                    "low": float(row[3]),   # Low is in the 4th column
                    "close": float(row[1]),  # Close is in the 2nd column
                    "volume": int(float(row[5])) if not pd.isna(row[5]) else 0  # Volume is in the 6th column
                })
            except Exception as e:
                logger.warning(f"Error processing ES data row: {e}")
                continue

        return {
            "data": chart_data,
            "count": len(chart_data),
            "last_updated": es_file_path.stat().st_mtime
        }

    except Exception as e:
        logger.error(f"Error serving ES futures data: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/vix", response_model=FuturesDataResponse)
async def get_vix_futures():
    """API endpoint to get VIX futures data."""
    try:
        # Path to the futures data
        data_dir = Path(__file__).resolve().parent.parent.parent.parent.parent / "data" / "market_data" / "futures"
        vix_file_path = data_dir / "vix_futures.csv"

        # Check if the file exists
        if not vix_file_path.exists():
            logger.error(f"VIX futures data file not found at {vix_file_path}")
            raise HTTPException(
                status_code=404,
                detail="VIX futures data not available. Please run the data fetching script first."
            )

        # Read the data
        vix_data = pd.read_csv(vix_file_path, skiprows=2)  # Skip the first two rows (headers)

        # Convert to JSON format suitable for charts
        chart_data = []
        for _, row in vix_data.iterrows():
            try:
                # Check if we have enough data in the row
                if len(row) >= 6:
                    chart_data.append({
                        "date": row[0],  # Date is in the first column
                        "open": float(row[4]) if not pd.isna(row[4]) else 0,  # Open is in the 5th column
                        "high": float(row[2]) if not pd.isna(row[2]) else 0,  # High is in the 3rd column
                        "low": float(row[3]) if not pd.isna(row[3]) else 0,   # Low is in the 4th column
                        "close": float(row[1]) if not pd.isna(row[1]) else 0,  # Close is in the 2nd column
                        "volume": int(float(row[5])) if not pd.isna(row[5]) else 0  # Volume is in the 6th column
                    })
            except Exception as e:
                logger.warning(f"Error processing VIX data row: {e}")
                continue

        return {
            "data": chart_data,
            "count": len(chart_data),
            "last_updated": vix_file_path.stat().st_mtime
        }

    except Exception as e:
        logger.error(f"Error serving VIX futures data: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/refresh", response_model=RefreshResponse)
async def refresh_futures_data():
    """API endpoint to refresh futures data by running the data fetching script."""
    try:
        # Get the absolute path to the script
        script_dir = Path(__file__).resolve().parent.parent.parent.parent.parent
        script_path = script_dir / 'scripts' / 'fetch_futures_data.py'

        logger.info(f"Refreshing futures data using script at {script_path}")

        # Run the script
        result = subprocess.run(
            [sys.executable, str(script_path)],
            check=True,
            capture_output=True,
            text=True
        )

        logger.info(f"Futures data refresh output: {result.stdout}")

        data_dir = script_dir / 'data' / 'market_data' / 'futures'
        es_file_path = data_dir / "es_futures.csv"

        return {
            "success": True,
            "message": "Futures data refreshed successfully",
            "last_updated": es_file_path.stat().st_mtime
        }

    except Exception as e:
        logger.error(f"Error refreshing futures data: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
