"""
MacroAnalysis API

This is the main FastAPI application entry point.
"""

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.routers import trends, events, indicators, scenarios, futures

# Create FastAPI app
app = FastAPI(
    title="MacroAnalysis API",
    description="API for analyzing high-impact forex economic events",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001", "http://localhost:3002"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(trends.router)
app.include_router(events.router)
app.include_router(indicators.router, prefix="/api/indicators", tags=["indicators"])
app.include_router(scenarios.router, prefix="/api/scenarios", tags=["scenarios"])
app.include_router(futures.router)

# Mount static files directory - commented out until directories are created
# static_dir = Path(__file__).parent.parent.parent.parent / "frontend" / "static"
# app.mount("/static", StaticFiles(directory=static_dir), name="static")

# Setup templates - commented out until directories are created
# templates_dir = Path(__file__).parent.parent.parent.parent / "frontend" / "templates"
# templates = Jinja2Templates(directory=templates_dir)
templates = None

@app.get("/")
async def get_index():
    """Return API info."""
    return {
        "message": "MacroAnalysis API is running",
        "version": "1.0.0",
        "docs_url": "/docs"
    }

@app.get("/trends")
async def get_trends_page():
    """Return API info for trends."""
    return {
        "message": "Use the /api/trends endpoints for trend data",
        "version": "1.0.0"
    }

@app.get("/events")
async def get_events_page():
    """Return API info for events."""
    return {
        "message": "Use the /api/events endpoints for event data",
        "version": "1.0.0"
    }

@app.get("/scenarios")
async def get_scenarios_page():
    """Return API info for scenarios."""
    return {
        "message": "Use the /api/scenarios endpoints for scenario analysis data",
        "version": "1.0.0"
    }

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "version": "1.0.0"}
