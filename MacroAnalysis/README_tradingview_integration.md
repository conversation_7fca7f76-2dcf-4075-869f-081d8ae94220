# TradingView Chart Integration with Price Levels

## Overview

This enhancement adds TradingView charts to the Daily Fundamentals tab, providing professional-grade financial charts with support for custom price levels. Users can toggle between our custom-built charts and the TradingView charts depending on their preferences and needs.

## Features

### TradingView Integration
- Professional-grade financial charts
- Advanced technical indicators
- Multiple timeframes (1m to Monthly)
- Support for multiple futures contracts
- Real-time data from market feeds
- Full suite of drawing tools
- Responsive design for all device sizes

### Price Levels Management
- Add custom support and resistance levels
- Customizable appearance (color, line style, width)
- Custom labels for each level
- Visual representation directly on the chart
- Manage all price levels from a dedicated panel
- Clear visual distinction between support and resistance levels

### Symbol Support
- ES (E-mini S&P 500 Futures)
- NQ (E-mini NASDAQ Futures)
- YM (E-mini Dow Futures)
- RTY (E-mini Russell 2000 Futures)
- VIX (Volatility Index)
- VX (VIX Futures)
- GC (Gold Futures)
- SI (Silver Futures)
- CL (Crude Oil Futures)
- NG (Natural Gas Futures)
- ZB (30-Year U.S. Treasury Bond Futures)
- ZN (10-Year U.S. Treasury Note Futures)
- ZF (5-Year U.S. Treasury Note Futures)
- ZT (2-Year U.S. Treasury Note Futures)

## Implementation Details

### Architecture
- TradingView Widget API integration
- Price levels management with React components
- Theme synchronization with main application
- Symbol synchronization with tab selection
- Component-based architecture for easy extensibility

### Technical Implementation
- Dynamic script loading for TradingView widget
- React useRef and useEffect hooks for proper lifecycle management
- Event-based communication between components
- Responsive CSS for all screen sizes
- Clean, modular code structure

## Usage

### Accessing TradingView Charts
1. Navigate to the "Daily Fundamentals" tab
2. Click the "TradingView" toggle button in the header
3. The TradingView chart will load with the currently selected symbol

### Setting Chart Options
1. Use the Symbol selector to change the displayed futures contract
2. Use the Timeframe selector to change the chart's time resolution
3. Use TradingView's built-in tools for additional customization

### Managing Price Levels
1. Use the "Price Levels" panel on the right side of the chart
2. Click the "+ Add Price Level" button to create a new level
3. Enter the price value, select the level type (support/resistance), and customize its appearance
4. Add a custom label if desired
5. Click "Add Level" to add it to the chart
6. Existing levels can be removed by clicking the "✕" button next to each level

### Switching Between Chart Types
- Use the "Custom Charts / TradingView" toggle in the header to switch between chart types
- Your selected symbol (ES/VIX) will be carried over between chart types

## Technical Notes

### TradingView Widget API
- Uses the TradingView Widget API to embed charts
- Minimal delay in loading TradingView's external scripts
- Cleanup function to properly handle component unmounting
- Fallback for browsers with script blockers

### Price Levels Implementation
- Price levels are drawn using TradingView's shapes API
- Levels persist during chart navigation and timeframe changes
- Automatic adjustment to the visible range of the chart
- Efficient redrawing when price levels are modified

### Theme Integration
- Synchronization with the application's theme system
- Dark/Light/Blue themes supported
- Consistent visual experience across components

## Future Enhancements

- Save and load price level sets
- Alert/notification when price crosses a level
- Additional level types (trend lines, Fibonacci, etc.)
- Price level statistics and analytics
- Default level sets for common market scenarios
- Historical level performance tracking