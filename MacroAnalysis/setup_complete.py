#!/usr/bin/env python3
"""
TradingView Live Charts Setup Script - Complete Version
Automated setup for the TradingView live chart system
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 60)
    print(f"🚀 {title}")
    print("=" * 60)

def print_step(step, description):
    """Print a formatted step"""
    print(f"\n📋 Step {step}: {description}")
    print("-" * 40)

def run_command(command, description=""):
    """Run a shell command and handle errors"""
    try:
        if description:
            print(f"   Running: {description}")
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        if result.stdout:
            print(f"   ✅ {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"   ❌ Error: {e.stderr.strip()}")
        return False

def create_directory_structure():
    """Create the necessary directory structure"""
    print_step(1, "Creating Directory Structure")
    
    directories = [
        "live_charts",
        "live_charts/backend",
        "live_charts/frontend",
        "live_charts/static",
        "live_charts/templates"
    ]
    
    for directory in directories:
        dir_path = Path(directory)
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"   ✅ Created: {directory}")

def create_backend_files():
    """Create backend application files"""
    print_step(2, "Creating Backend Files")
    
    # Main Flask application
    app_content = '''"""
TradingView Live Chart Backend API
Flask application for TradingView integration
"""

from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import os
import time
from datetime import datetime, timedelta
import logging
from dataclasses import dataclass
from typing import Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

@dataclass
class TradingViewCredentials:
    username: str
    password: str
    session_token: Optional[str] = None
    expires_at: Optional[datetime] = None

class TradingViewClient:
    def __init__(self):
        self.credentials = None
        self.authenticated = False
        
    def authenticate(self, username: str, password: str) -> bool:
        """Simplified authentication for demo purposes"""
        try:
            # Simulate authentication delay
            time.sleep(1)
            
            # Basic validation (replace with actual TradingView auth)
            if username and password and len(username) > 0 and len(password) > 0:
                self.credentials = TradingViewCredentials(
                    username=username,
                    password=password,
                    session_token=f"demo_session_{int(time.time())}",
                    expires_at=datetime.now() + timedelta(hours=1)
                )
                self.authenticated = True
                logger.info(f"Demo authentication successful for user: {username}")
                return True
            else:
                return False
                
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            return False
    
    def is_authenticated(self) -> bool:
        """Check if client is authenticated and session is valid"""
        if not self.credentials:
            return False
        
        if self.credentials.expires_at and datetime.now() > self.credentials.expires_at:
            self.authenticated = False
            return False
            
        return self.authenticated

# Global client instance
tv_client = TradingViewClient()

@app.route('/')
def serve_frontend():
    """Serve the frontend application"""
    return send_from_directory('../frontend', 'index.html')

@app.route('/static/<path:filename>')
def serve_static(filename):
    """Serve static files"""
    return send_from_directory('../static', filename)

@app.route('/api/auth', methods=['POST'])
def authenticate():
    """Authenticate with TradingView credentials"""
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        if not username or not password:
            return jsonify({
                'success': False,
                'error': 'Username and password are required'
            }), 400
        
        success = tv_client.authenticate(username, password)
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Authentication successful',
                'username': username,
                'session_expires': tv_client.credentials.expires_at.isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Invalid credentials'
            }), 401
            
    except Exception as e:
        logger.error(f"Authentication error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Authentication service error'
        }), 500

@app.route('/api/status')
def get_status():
    """Get authentication status"""
    return jsonify({
        'authenticated': tv_client.is_authenticated(),
        'username': tv_client.credentials.username if tv_client.credentials else None,
        'expires_at': tv_client.credentials.expires_at.isoformat() if tv_client.credentials and tv_client.credentials.expires_at else None
    })

@app.route('/api/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    print("🚀 Starting TradingView Live Chart Backend...")
    print("🌐 Frontend: http://localhost:5000")
    print("📡 API: http://localhost:5000/api/")
    app.run(debug=True, host='0.0.0.0', port=5000)
'''
    
    with open('live_charts/backend/app.py', 'w') as f:
        f.write(app_content)
    print("   ✅ Created: backend/app.py")

def create_frontend_files():
    """Create frontend HTML file"""
    print_step(3, "Creating Frontend Files")
    
    # Create the simplified frontend HTML based on our artifact
    html_content = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradingView Live Charts</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container { min-height: 100vh; display: flex; flex-direction: column; }
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }
        .logo { font-size: 1.5rem; font-weight: 700; color: #2c3e50; }
        .main-content {
            flex: 1;
            display: grid;
            grid-template-columns: 350px 1fr;
            gap: 1rem;
            padding: 1rem;
        }
        .sidebar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            height: fit-content;
        }
        .chart-area {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }
        .chart-container { flex: 1; height: 700px; position: relative; }
        .form-group { margin-bottom: 1rem; }
        .form-group label { display: block; margin-bottom: 0.5rem; font-weight: 500; }
        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
        }
        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            width: 100%;
            transition: transform 0.2s ease;
        }
        .btn:hover { transform: translateY(-2px); }
        .btn:disabled { opacity: 0.6; cursor: not-allowed; }
        .status-message { padding: 1rem; border-radius: 8px; margin: 1rem 0; }
        .status-success { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .status-warning { background: #fff3cd; color: #856404; }
        .instructions {
            margin-top: 2rem;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        .instructions h4 { color: #2c3e50; margin-bottom: 1rem; }
        .instructions ul { margin-left: 1.5rem; color: #555; }
        .instructions li { margin-bottom: 0.5rem; }
        .chart-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 10;
        }
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        @media (max-width: 1024px) {
            .main-content { grid-template-columns: 1fr; }
            .sidebar { order: 2; }
            .chart-area { order: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">📈 TradingView Live Charts</div>
            <div id="userStatus">Not Connected</div>
        </header>

        <main class="main-content">
            <aside class="sidebar">
                <div class="auth-panel">
                    <h3>🔐 TradingView Authentication</h3>
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" id="username" placeholder="Enter TradingView username">
                    </div>
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" placeholder="Enter password">
                    </div>
                    <button class="btn" id="authBtn" onclick="authenticate()">🔑 Connect</button>
                    <div id="authStatus"></div>
                </div>

                <div class="instructions">
                    <h4>📝 Getting Started</h4>
                    <ul>
                        <li>Enter your TradingView credentials</li>
                        <li>Click "Connect" to authenticate</li>
                        <li>Live charts will load automatically</li>
                        <li>Session remains active for trading</li>
                    </ul>
                </div>
            </aside>

            <section class="chart-area">
                <div class="chart-header">
                    <div class="chart-title">TradingView Live Chart</div>
                    <div id="chartInfo">Please authenticate to load charts</div>
                </div>
                
                <div id="chartContainer" class="chart-container">
                    <div class="loading-overlay" id="loadingOverlay">
                        <div class="loading-spinner"></div>
                        <div>Please authenticate to load live charts</div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script src="https://s3.tradingview.com/tv.js"></script>
    <script>
        let isAuthenticated = false;
        let currentUser = null;
        let widget = null;

        async function authenticate() {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const authBtn = document.getElementById('authBtn');
            const authStatus = document.getElementById('authStatus');

            if (!username || !password) {
                authStatus.innerHTML = '<div class="status-message status-error">Please enter credentials</div>';
                return;
            }

            authBtn.disabled = true;
            authBtn.textContent = '🔄 Connecting...';
            authStatus.innerHTML = '<div class="status-message status-warning">Authenticating...</div>';

            try {
                const response = await fetch('/api/auth', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({username, password})
                });

                const result = await response.json();

                if (result.success) {
                    isAuthenticated = true;
                    currentUser = username;
                    
                    authStatus.innerHTML = '<div class="status-message status-success">Connected successfully!</div>';
                    document.getElementById('userStatus').textContent = `Connected - ${username}`;
                    document.getElementById('chartInfo').textContent = `Connected as ${username}`;
                    
                    await loadChart();
                    
                    authBtn.textContent = '✅ Connected';
                    authBtn.disabled = true;
                } else {
                    authStatus.innerHTML = `<div class="status-message status-error">Error: ${result.error}</div>`;
                }
            } catch (error) {
                authStatus.innerHTML = `<div class="status-message status-error">Connection error: ${error.message}</div>`;
            } finally {
                if (!isAuthenticated) {
                    authBtn.disabled = false;
                    authBtn.textContent = '🔑 Connect';
                }
            }
        }

        async function loadChart() {
            const container = document.getElementById('chartContainer');
            const loading = document.getElementById('loadingOverlay');
            
            loading.innerHTML = '<div class="loading-spinner"></div><div>Loading TradingView chart...</div>';

            try {
                widget = new TradingView.widget({
                    autosize: true,
                    symbol: "NASDAQ:AAPL",
                    interval: "D",
                    timezone: "Etc/UTC",
                    theme: "light",
                    style: "1",
                    locale: "en",
                    toolbar_bg: "#f1f3f6",
                    enable_publishing: false,
                    allow_symbol_change: true,
                    details: true,
                    hotlist: true,
                    studies: ["Volume@tv-basicstudies"],
                    container_id: "chartContainer"
                });

                setTimeout(() => {
                    loading.style.display = 'none';
                }, 3000);

            } catch (error) {
                console.error('Chart loading error:', error);
                loading.innerHTML = '<div>Failed to load chart. Please refresh and try again.</div>';
            }
        }

        async function checkAuthStatus() {
            try {
                const response = await fetch('/api/status');
                const result = await response.json();
                
                if (result.authenticated && result.username) {
                    isAuthenticated = true;
                    currentUser = result.username;
                    document.getElementById('userStatus').textContent = `Connected - ${result.username}`;
                    document.getElementById('chartInfo').textContent = `Connected as ${result.username}`;
                    document.getElementById('authBtn').textContent = '✅ Connected';
                    document.getElementById('authBtn').disabled = true;
                    await loadChart();
                }
            } catch (error) {
                console.log('No existing session');
            }
        }

        document.getElementById('password').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') authenticate();
        });

        document.addEventListener('DOMContentLoaded', checkAuthStatus);
    </script>
</body>
</html>'''
    
    with open('live_charts/frontend/index.html', 'w') as f:
        f.write(html_content)
    print("   ✅ Created: frontend/index.html")

def create_config_files():
    """Create configuration files"""
    print_step(4, "Creating Configuration Files")
    
    # Requirements file
    requirements_content = """flask>=2.3.0
flask-cors>=4.0.0
requests>=2.31.0
python-dotenv>=1.0.0
"""
    
    with open('live_charts/requirements.txt', 'w') as f:
        f.write(requirements_content)
    print("   ✅ Created: requirements.txt")

def create_run_scripts():
    """Create convenient run scripts"""
    print_step(5, "Creating Run Scripts")
    
    # Python run script
    run_script = """#!/usr/bin/env python3
import os
import sys
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent / 'backend'
sys.path.insert(0, str(backend_path))

# Change to backend directory
os.chdir(backend_path)

# Import and run the app
from app import app

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
"""
    
    with open('live_charts/run.py', 'w') as f:
        f.write(run_script)
    os.chmod('live_charts/run.py', 0o755)
    print("   ✅ Created: run.py")

def create_documentation():
    """Create README and documentation"""
    print_step(6, "Creating Documentation")
    
    readme_content = """# TradingView Live Charts

A professional web application for displaying live TradingView charts with authentication.

## 🚀 Quick Start

1. **Install Dependencies:**
   ```bash
   cd live_charts
   pip install -r requirements.txt
   ```

2. **Run the Application:**
   ```bash
   python run.py
   ```

3. **Access the Application:**
   Open: http://localhost:5000

## 📋 Features

- 🔐 TradingView authentication
- 📊 Live chart integration  
- 💻 Professional web interface
- 🎨 Modern responsive design
- ⚡ Real-time data display

## 🏗️ Project Structure

```
live_charts/
├── backend/
│   └── app.py          # Flask backend
├── frontend/
│   └── index.html      # Web interface
├── requirements.txt    # Dependencies
├── run.py             # Start script
└── README.md          # This file
```

## 🔧 API Endpoints

- `POST /api/auth` - Authenticate
- `GET /api/status` - Check status  
- `GET /api/health` - Health check

## 💡 Usage

1. Enter TradingView credentials
2. Click "Connect" 
3. Live charts load automatically
4. Enjoy professional trading interface!

## 🔒 Security Notes

This is a demo implementation. For production:
- Implement proper OAuth
- Add HTTPS/SSL
- Secure credential storage
- Add rate limiting

## 📞 Support

Built with Flask + TradingView Widget API
Ready for development and testing!
"""
    
    with open('live_charts/README.md', 'w') as f:
        f.write(readme_content)
    print("   ✅ Created: README.md")

def main():
    """Main setup function"""
    print_header("TradingView Live Charts Setup")
    
    try:
        create_directory_structure()
        create_backend_files()
        create_frontend_files()
        create_config_files()
        create_run_scripts()
        create_documentation()
        
        print_header("Setup Complete! 🎉")
        
        print("\n📁 Project created in: ./live_charts/")
        print("🚀 To start the application:")
        print("   cd live_charts")
        print("   pip install -r requirements.txt")
        print("   python run.py")
        print("\n🌐 Then open: http://localhost:5000")
        print("\n📖 For more information, see: live_charts/README.md")
        print("\n" + "=" * 60)
        print("🎯 Ready to trade with TradingView live charts!")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Setup failed: {str(e)}")
        print("Please check the error and try again.")
        sys.exit(1)

if __name__ == '__main__':
    main()
